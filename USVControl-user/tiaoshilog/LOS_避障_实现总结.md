# LOS导航和局部避障实现总结

## 概述
成功实现了基于原有代码的LOS（Line of Sight）导航算法和局部避障功能，提升了无人船的路径跟踪精度和安全性。

## 主要改进

### 1. 启用原有的LOS算法
- **位置**: `motion_plan.cpp` 第574-595行
- **功能**: 启用了原本注释掉的`Dot2Line`函数调用
- **算法**: 
  - 计算船舶到航迹线的最近点
  - 在最近点基础上计算150米前视点
  - 使用前视点作为导航目标，实现平滑的轨迹跟踪

### 2. 实现局部避障算法
- **核心函数**: 
  - `WorldToPixel()`: 世界坐标转像素坐标
  - `DetectObstacle()`: 指定方向障碍物检测
  - `LocalObstacleAvoidance()`: 局部避障主算法

- **避障策略**:
  - 检测前方100米范围内的障碍物
  - 如果障碍物距离小于15米安全边距，启动避障
  - 在期望航向左右±45度范围内搜索最佳避障方向
  - 优先选择偏离角度最小的可行方向

### 3. 集成到主控制流程
- **修改**: `TrajectoryTrackingDirect()`函数签名，添加地图参数
- **集成点**: 
  - 阶段1（航向目标点）: 第492-510行
  - 阶段2（LOS轨迹跟踪）: 第656-674行
- **调用**: `planning_main.cpp`第488行传入`premap`地图数据

### 4. 调试和日志功能
- **日志文件**: `tiaoshilog/navigation_debug.log`
- **记录内容**: 避障事件、航向变化、船舶位置等
- **函数**: `LogDebugInfo()`

## 技术参数

### LOS导航参数
- 前视距离: 150米（在Dot2Line计算中设定）
- 航点切换距离: 50米
- 地图分辨率: 1米/像素

### 避障参数
- 障碍物检测范围: 100米
- 安全边距: 15米
- 最大避障角度: ±45度
- 检测步长: 1米
- 地图尺寸: 800x800像素

## 算法流程

### LOS导航流程
1. 使用`Dot2Line()`计算船舶到当前航迹线的最近点
2. 在最近点基础上沿航迹线方向前进150米得到前视点
3. 计算朝向前视点的航向角
4. 应用局部避障修正航向角

### 避障流程
1. 检测期望航向前方是否有障碍物
2. 如果障碍物距离 < 安全边距，启动避障
3. 在±45度范围内以5度步长搜索可行方向
4. 选择第一个无障碍物或障碍物距离足够远的方向
5. 返回修正后的航向角

## 地图坐标系统
- **原点**: 地图中心(400,400)对应船舶当前位置
- **坐标转换**: 
  - X轴: 像素X = 400 + 世界X/分辨率
  - Y轴: 像素Y = 400 - 世界Y/分辨率（Y轴翻转）
- **障碍物表示**: premap[i][j] = 1表示障碍物，0表示自由空间

## 编译结果
- ✅ 编译成功
- ⚠️ 有一些非关键警告（未使用变量等）
- 📁 可执行文件生成在`build/xt_user/`目录

## 使用说明
1. 系统会自动启用LOS导航和局部避障
2. 调试信息输出到`tiaoshilog/navigation_debug.log`
3. 可通过修改`losNavigation`结构体参数调整避障行为
4. 地图数据通过`EnvironCallback`函数实时更新

## 下一步建议
1. 根据实际测试调整避障参数
2. 优化坐标转换精度
3. 添加更多调试信息
4. 考虑动态调整前视距离
