AMENT_PREFIX_PATH=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble
CMAKE_PREFIX_PATH=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user
COLCON=1
COLCON_PREFIX_PATH=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install
COLORTERM=truecolor
CONDA_DEFAULT_ENV=base
CONDA_EXE=/home/<USER>/miniconda3/bin/conda
CONDA_PREFIX=/home/<USER>/miniconda3
CONDA_PROMPT_MODIFIER=(base)
CONDA_PYTHON_EXE=/home/<USER>/miniconda3/bin/python
CONDA_SHLVL=1
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus,guid=a80ebece4cd642bc3811e4ed689dd886
DBUS_STARTER_ADDRESS=unix:path=/run/user/1000/bus,guid=a80ebece4cd642bc3811e4ed689dd886
DBUS_STARTER_BUS_TYPE=session
DESKTOP_SESSION=ubuntu
DISPLAY=:0
GDMSESSION=ubuntu
GNOME_DESKTOP_SESSION_ID=this-is-deprecated
GNOME_SETUP_DISPLAY=:1
GNOME_SHELL_SESSION_MODE=ubuntu
GNOME_TERMINAL_SCREEN=/org/gnome/Terminal/screen/721768e7_f2ba_4d45_aa27_4186da3e9312
GNOME_TERMINAL_SERVICE=:1.142
GTK_MODULES=gail:atk-bridge
HOME=/home/<USER>
HTTPS_PROXY=http://127.0.0.1:7890/
HTTP_PROXY=http://127.0.0.1:7890/
IM_CONFIG_PHASE=1
LANG=zh_CN.UTF-8
LANGUAGE=zh_CN:en_US:en
LC_ADDRESS=zh_CN.UTF-8
LC_IDENTIFICATION=zh_CN.UTF-8
LC_MEASUREMENT=zh_CN.UTF-8
LC_MONETARY=zh_CN.UTF-8
LC_NAME=zh_CN.UTF-8
LC_NUMERIC=zh_CN.UTF-8
LC_PAPER=zh_CN.UTF-8
LC_TELEPHONE=zh_CN.UTF-8
LC_TIME=zh_CN.UTF-8
LD_LIBRARY_PATH=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOGNAME=haique
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
NO_PROXY=localhost,*********/8,::1
PAPERSIZE=a4
PATH=/opt/ros/humble/bin:/home/<USER>/miniconda3/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin
PWD=/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
PYTHONPATH=/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages
QT_ACCESSIBILITY=1
QT_IM_MODULE=ibus
ROS_DISTRO=humble
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SESSION_MANAGER=local/haique:@/tmp/.ICE-unix/1826,unix/haique:/tmp/.ICE-unix/1826
SHELL=/bin/bash
SHLVL=1
SSH_AGENT_LAUNCHER=gnome-keyring
SSH_AUTH_SOCK=/run/user/1000/keyring/ssh
SYSTEMD_EXEC_PID=1826
TERM=xterm-256color
USER=haique
USERNAME=haique
VTE_VERSION=6800
WAYLAND_DISPLAY=wayland-0
XAUTHORITY=/run/user/1000/.mutter-Xwaylandauth.6ROQA3
XDG_CONFIG_DIRS=/etc/xdg/xdg-ubuntu:/etc/xdg
XDG_CURRENT_DESKTOP=ubuntu:GNOME
XDG_DATA_DIRS=/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop
XDG_MENU_PREFIX=gnome-
XDG_RUNTIME_DIR=/run/user/1000
XDG_SESSION_CLASS=user
XDG_SESSION_DESKTOP=ubuntu
XDG_SESSION_TYPE=wayland
XMODIFIERS=@im=ibus
_=/usr/bin/colcon
http_proxy=http://127.0.0.1:7890/
https_proxy=http://127.0.0.1:7890/
no_proxy=localhost,*********/8,::1
