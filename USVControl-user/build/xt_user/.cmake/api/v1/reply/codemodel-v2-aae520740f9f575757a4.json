{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-3e5c4dd3bf27747a7adb.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "xt_user", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "planning_hpp::@6890427a1f51a3e7e1df", "jsonFile": "target-planning_hpp-6b55da847b0ac8cf7a9d.json", "name": "planning_hpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-f4d13bfd00e9de3f2234.json", "name": "uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "xt_user_node::@6890427a1f51a3e7e1df", "jsonFile": "target-xt_user_node-2540dc2285d41489a63c.json", "name": "xt_user_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "xt_user_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-xt_user_uninstall-f380a00ede7736526da7.json", "name": "xt_user_uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user", "source": "/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning"}, "version": {"major": 2, "minor": 3}}