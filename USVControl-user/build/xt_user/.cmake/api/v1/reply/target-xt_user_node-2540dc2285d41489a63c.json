{"artifacts": [{"path": "xt_user_node"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 41, "parent": 0}, {"command": 1, "file": 0, "line": 42, "parent": 0}, {"command": 2, "file": 0, "line": 43, "parent": 0}, {"command": 1, "file": 1, "line": 145, "parent": 3}, {"command": 3, "file": 0, "line": 9, "parent": 0}, {"command": 4, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"backtrace": 5, "fragment": "-Wall"}, {"backtrace": 5, "fragment": "-Wextra"}, {"backtrace": 5, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 2, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 2, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 6, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/cv_bridge"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 2, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 2, "isSystem": true, "path": "/usr/include/opencv4"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "planning_hpp::@6890427a1f51a3e7e1df"}], "id": "xt_user_node::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user:/opt/ros/humble/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "libplanning_hpp.so", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.5.4d", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libcv_bridge.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.5.4d", "role": "libraries"}, {"backtrace": 2, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.5.4d", "role": "libraries"}], "language": "CXX"}, "name": "xt_user_node", "nameOnDisk": "xt_user_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main/planning_main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}