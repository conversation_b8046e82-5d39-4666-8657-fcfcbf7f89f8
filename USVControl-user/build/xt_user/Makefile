# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/333/project2_0815/USVControl-user/build/xt_user

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/333/project2_0815/USVControl-user/build/xt_user/CMakeFiles /home/<USER>/333/project2_0815/USVControl-user/build/xt_user//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/333/project2_0815/USVControl-user/build/xt_user/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named uninstall

# Build rule for target.
uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 uninstall
.PHONY : uninstall

# fast build rule for target.
uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/uninstall.dir/build.make CMakeFiles/uninstall.dir/build
.PHONY : uninstall/fast

#=============================================================================
# Target rules for targets named xt_user_uninstall

# Build rule for target.
xt_user_uninstall: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 xt_user_uninstall
.PHONY : xt_user_uninstall

# fast build rule for target.
xt_user_uninstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xt_user_uninstall.dir/build.make CMakeFiles/xt_user_uninstall.dir/build
.PHONY : xt_user_uninstall/fast

#=============================================================================
# Target rules for targets named planning_hpp

# Build rule for target.
planning_hpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 planning_hpp
.PHONY : planning_hpp

# fast build rule for target.
planning_hpp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/build
.PHONY : planning_hpp/fast

#=============================================================================
# Target rules for targets named xt_user_node

# Build rule for target.
xt_user_node: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 xt_user_node
.PHONY : xt_user_node

# fast build rule for target.
xt_user_node/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xt_user_node.dir/build.make CMakeFiles/xt_user_node.dir/build
.PHONY : xt_user_node/fast

controller/fuzzy_pid_control.o: controller/fuzzy_pid_control.cpp.o
.PHONY : controller/fuzzy_pid_control.o

# target to build an object file
controller/fuzzy_pid_control.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o
.PHONY : controller/fuzzy_pid_control.cpp.o

controller/fuzzy_pid_control.i: controller/fuzzy_pid_control.cpp.i
.PHONY : controller/fuzzy_pid_control.i

# target to preprocess a source file
controller/fuzzy_pid_control.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.i
.PHONY : controller/fuzzy_pid_control.cpp.i

controller/fuzzy_pid_control.s: controller/fuzzy_pid_control.cpp.s
.PHONY : controller/fuzzy_pid_control.s

# target to generate assembly for a file
controller/fuzzy_pid_control.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.s
.PHONY : controller/fuzzy_pid_control.cpp.s

controller/motion_control.o: controller/motion_control.cpp.o
.PHONY : controller/motion_control.o

# target to build an object file
controller/motion_control.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o
.PHONY : controller/motion_control.cpp.o

controller/motion_control.i: controller/motion_control.cpp.i
.PHONY : controller/motion_control.i

# target to preprocess a source file
controller/motion_control.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.i
.PHONY : controller/motion_control.cpp.i

controller/motion_control.s: controller/motion_control.cpp.s
.PHONY : controller/motion_control.s

# target to generate assembly for a file
controller/motion_control.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.s
.PHONY : controller/motion_control.cpp.s

controller/pid_control.o: controller/pid_control.cpp.o
.PHONY : controller/pid_control.o

# target to build an object file
controller/pid_control.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o
.PHONY : controller/pid_control.cpp.o

controller/pid_control.i: controller/pid_control.cpp.i
.PHONY : controller/pid_control.i

# target to preprocess a source file
controller/pid_control.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.i
.PHONY : controller/pid_control.cpp.i

controller/pid_control.s: controller/pid_control.cpp.s
.PHONY : controller/pid_control.s

# target to generate assembly for a file
controller/pid_control.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.s
.PHONY : controller/pid_control.cpp.s

controller/smc_control.o: controller/smc_control.cpp.o
.PHONY : controller/smc_control.o

# target to build an object file
controller/smc_control.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o
.PHONY : controller/smc_control.cpp.o

controller/smc_control.i: controller/smc_control.cpp.i
.PHONY : controller/smc_control.i

# target to preprocess a source file
controller/smc_control.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.i
.PHONY : controller/smc_control.cpp.i

controller/smc_control.s: controller/smc_control.cpp.s
.PHONY : controller/smc_control.s

# target to generate assembly for a file
controller/smc_control.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.s
.PHONY : controller/smc_control.cpp.s

home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.o: home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o
.PHONY : home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.o

# target to build an object file
home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/home/<USER>/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o
.PHONY : home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o

home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.i: home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.i
.PHONY : home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.i

# target to preprocess a source file
home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/home/<USER>/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.i
.PHONY : home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.i

home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.s: home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.s
.PHONY : home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.s

# target to generate assembly for a file
home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/home/<USER>/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.s
.PHONY : home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.s

main/planning_main.o: main/planning_main.cpp.o
.PHONY : main/planning_main.o

# target to build an object file
main/planning_main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xt_user_node.dir/build.make CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o
.PHONY : main/planning_main.cpp.o

main/planning_main.i: main/planning_main.cpp.i
.PHONY : main/planning_main.i

# target to preprocess a source file
main/planning_main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xt_user_node.dir/build.make CMakeFiles/xt_user_node.dir/main/planning_main.cpp.i
.PHONY : main/planning_main.cpp.i

main/planning_main.s: main/planning_main.cpp.s
.PHONY : main/planning_main.s

# target to generate assembly for a file
main/planning_main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/xt_user_node.dir/build.make CMakeFiles/xt_user_node.dir/main/planning_main.cpp.s
.PHONY : main/planning_main.cpp.s

motion_plan/motion_plan.o: motion_plan/motion_plan.cpp.o
.PHONY : motion_plan/motion_plan.o

# target to build an object file
motion_plan/motion_plan.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o
.PHONY : motion_plan/motion_plan.cpp.o

motion_plan/motion_plan.i: motion_plan/motion_plan.cpp.i
.PHONY : motion_plan/motion_plan.i

# target to preprocess a source file
motion_plan/motion_plan.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.i
.PHONY : motion_plan/motion_plan.cpp.i

motion_plan/motion_plan.s: motion_plan/motion_plan.cpp.s
.PHONY : motion_plan/motion_plan.s

# target to generate assembly for a file
motion_plan/motion_plan.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/planning_hpp.dir/build.make CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.s
.PHONY : motion_plan/motion_plan.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... uninstall"
	@echo "... xt_user_uninstall"
	@echo "... planning_hpp"
	@echo "... xt_user_node"
	@echo "... controller/fuzzy_pid_control.o"
	@echo "... controller/fuzzy_pid_control.i"
	@echo "... controller/fuzzy_pid_control.s"
	@echo "... controller/motion_control.o"
	@echo "... controller/motion_control.i"
	@echo "... controller/motion_control.s"
	@echo "... controller/pid_control.o"
	@echo "... controller/pid_control.i"
	@echo "... controller/pid_control.s"
	@echo "... controller/smc_control.o"
	@echo "... controller/smc_control.i"
	@echo "... controller/smc_control.s"
	@echo "... home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.o"
	@echo "... home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.i"
	@echo "... home/xumj/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.s"
	@echo "... main/planning_main.o"
	@echo "... main/planning_main.i"
	@echo "... main/planning_main.s"
	@echo "... motion_plan/motion_plan.o"
	@echo "... motion_plan/motion_plan.i"
	@echo "... motion_plan/motion_plan.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

