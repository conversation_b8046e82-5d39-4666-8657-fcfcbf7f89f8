#ifndef _MOTION_CONTROL_H
#define _MOTION_CONTROL_H

#include <string.h>
#include <termios.h>
#include <vector>
#include "../../include/Typedef.h"
#include "../../include/boat_datadef.h"
#include "pid_control.h"
#include "fuzzy_pid_control.h" // 保持PID头文件

using namespace std;

class MotionControl
{
public:
	MotionControl();
	
	void SetController(char pretype);

	char usvtype;

	PIDControl headingcontroller;
	FuzzyPIDControl fuzzyheadingcontroller; // 保持FuzzyPIDControl实例
	PIDControl velocitycontroller;

	actuator_control USVControl(headvel_control target, TransState boatstate);
};

#endif