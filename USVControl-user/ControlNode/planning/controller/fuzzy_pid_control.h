#ifndef _FUZZY_PID_CONTROL_H
#define _FUZZY_PID_CONTROL_H

#include <vector>
#include <cmath>
#include <algorithm>

class FuzzyPIDControl
{
public:
    FuzzyPIDControl();
    void Init(float p, float i, float d, float max_sum, float min_output, float max_output);
    float Fuzzy_PID_Control(float error);

    void EnableFuzzy(bool enable);
    
    // 新增：临时参数设置接口
    void SetTemporaryParams(float p, float i, float d);
    void ResetToBaseParams();
    
    // 获取内部状态的接口
    float GetKp() const { return kp; }
    float GetKi() const { return ki; }
    float GetKd() const { return kd; }
    float GetSumErr() const { return sumerr; }
    float GetDeltaKp() const { return delta_kp; }
    float GetDeltaKi() const { return delta_ki; }
    float GetDeltaKd() const { return delta_kd; }
    float GetError() const { return err; }
    float GetErrorChange() const { return last_ec; }

    // ===== [新增] 获取PID各分项输出的接口 =====
    float GetP_Out() const { return p_out; }
    float GetI_Out() const { return i_out; }
    float GetD_Out() const { return d_out; }
    // ==========================================

private:
    bool fuzzy_enabled;

    float kp_base, ki_base, kd_base;
    float kp, ki, kd;
    float err, olderr, sumerr;
    float MaxSum, MinOutput, MaxOutput;
    
    float last_ec;
    float delta_kp, delta_ki, delta_kd;

    // ===== [新增] 用于存储各分项输出的成员变量 =====
    float p_out, i_out, d_out;

    int rule_kp[7][7];
    int rule_ki[7][7];
    int rule_kd[7][7];

    float e_universe[2];
    float ec_universe[2];

    std::vector<float> delta_kp_levels;
    std::vector<float> delta_ki_levels;
    std::vector<float> delta_kd_levels;

    void setup_fuzzy_rules();
    void calculate_memberships(float value, const float universe[2], std::vector<float>& memberships);
    float get_output(const std::vector<float>& e_m, const std::vector<float>& ec_m, int rule_table[7][7], const std::vector<float>& output_levels);
};

#endif // _FUZZY_PID_CONTROL_H