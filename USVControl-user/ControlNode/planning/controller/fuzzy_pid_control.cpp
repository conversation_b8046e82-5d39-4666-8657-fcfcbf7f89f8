#include "fuzzy_pid_control.h"
#include <iostream>

FuzzyPIDControl::FuzzyPIDControl() {
    kp_base = 0; ki_base = 0; kd_base = 0;
    MaxSum = 0; MinOutput = 0; MaxOutput = 0;
    err = 0; olderr = 0; sumerr = 0;
    delta_kp = 0; delta_ki = 0; delta_kd = 0;
    last_ec = 0;
    p_out = 0; i_out = 0; d_out = 0; // 初始化新增成员
    fuzzy_enabled = true;
}

void FuzzyPIDControl::EnableFuzzy(bool enable) {
    this->fuzzy_enabled = enable;
}

// 新增：临时参数设置功能
void FuzzyPIDControl::SetTemporaryParams(float p, float i, float d) {
    kp = p;
    ki = i;
    kd = d;
}

void FuzzyPIDControl::ResetToBaseParams() {
    kp = kp_base;
    ki = ki_base;
    kd = kd_base;
}

void FuzzyPIDControl::Init(float p, float i, float d, float max_sum, float min_output, float max_output) {
    kp_base = p;
    ki_base = i;
    kd_base = d;
    kp = p;
    ki = i;
    kd = d;
    MaxSum = max_sum;
    MinOutput = min_output;
    MaxOutput = max_output;
    err = 0;
    olderr = 0;
    sumerr = 0;

    e_universe[0] = -180.0f; e_universe[1] = 180.0f;
    ec_universe[0] = -45.0f; ec_universe[1] = 45.0f;

    // 针对大惯性船只优化的参数调整范围
    // Kp调整范围：更大的调整范围以适应大惯性
    delta_kp_levels = {-2.0f, -1.2f, -0.6f, 0.0f, 0.6f, 1.2f, 2.0f};
    // Ki调整范围：保持较小的调整范围，避免积分饱和
    delta_ki_levels = {-0.005f, -0.003f, -0.001f, 0.0f, 0.001f, 0.003f, 0.005f};
    // Kd调整范围：更精确的阻尼调整
    delta_kd_levels = {-20.0f, -12.0f, -6.0f, 0.0f, 6.0f, 12.0f, 20.0f};

    setup_fuzzy_rules();
}

// setup_fuzzy_rules, calculate_memberships, get_output 函数保持不变
void FuzzyPIDControl::setup_fuzzy_rules() {
    int PB = 3, PM = 2, PS = 1, ZO = 0, NS = -1, NM = -2, NB = -3;
    
    // 针对大惯性船只优化的Kp规则：
    // - 大误差时更激进的响应
    // - 中等误差时保持稳定
    // - 小误差时精确控制
    int temp_rule_kp[7][7] = {
        {PB, PB, PB, PM, PS, ZO, NS}, {PB, PB, PB, PM, PS, ZO, NS}, {PB, PB, PM, PS, ZO, NS, NM},
        {PB, PB, PM, PS, ZO, NS, NM}, {PM, PM, PS, ZO, NS, NM, NB}, {PS, PS, ZO, NS, NM, NB, NB},
        {ZO, ZO, NS, NM, NB, NB, NB}
    };
    
    // 针对大惯性船只优化的Ki规则：
    // - 减少积分作用，避免累积误差
    // - 只在接近目标时启用积分
    int temp_rule_ki[7][7] = {
        {NB, NB, NM, NS, ZO, ZO, ZO}, {NB, NB, NM, NS, ZO, ZO, ZO}, {NM, NM, NS, ZO, PS, PS, PM},
        {NM, NS, ZO, ZO, PS, PM, PM}, {NS, ZO, ZO, PS, PS, PM, PB}, {ZO, ZO, PS, PS, PM, PB, PB},
        {ZO, ZO, PS, PS, PM, PB, PB}
    };
    
    // 针对大惯性船只优化的Kd规则：
    // - 增强阻尼作用，抑制超调
    // - 在误差变化大时提供更强的阻尼
    // - 接近目标时减少阻尼，提高响应速度
    int corrected_rule_kd[7][7] = {
        {ZO, PS, PM, PB, PB, PB, PB}, {NS, ZO, PS, PM, PB, PB, PB}, {NM, NS, ZO, PS, PM, PB, PB},
        {NB, NM, NS, ZO, PS, PM, PB}, {NB, NB, NM, NS, ZO, PS, PM}, {NB, NB, NB, NM, NS, ZO, PS},
        {NB, NB, NB, NM, NM, NS, ZO}
    };
    
    for(int i=0; i<7; ++i) {
        for(int j=0; j<7; ++j) {
            rule_kp[i][j] = temp_rule_kp[i][j];
            rule_ki[i][j] = temp_rule_ki[i][j];
            rule_kd[i][j] = corrected_rule_kd[i][j];
        }
    }
}
void FuzzyPIDControl::calculate_memberships(float value, const float universe[2], std::vector<float>& memberships) {
    float range = universe[1] - universe[0];
    float step = range / 6.0f;
    memberships.assign(7, 0.0f);
    if (value <= universe[0]) { memberships[0] = 1.0f; return; }
    if (value >= universe[1]) { memberships[6] = 1.0f; return; }
    for (int i = 0; i < 7; ++i) {
        float center = universe[0] + i * step;
        float dist = std::abs(value - center);
        if (dist <= step) { memberships[i] = 1.0f - (dist / step); }
        else { memberships[i] = 0.0f; }
    }
}
float FuzzyPIDControl::get_output(const std::vector<float>& e_m, const std::vector<float>& ec_m, int rule_table[7][7], const std::vector<float>& output_levels) {
    float numerator = 0.0f;
    float denominator = 0.0f;
    for (int i = 0; i < 7; ++i) {
        if (e_m[i] == 0) continue;
        for (int j = 0; j < 7; ++j) {
            if (ec_m[j] == 0) continue;
            float weight = std::min(e_m[i], ec_m[j]);
            if (weight == 0) continue;
            int rule_output_val = rule_table[i][j];
            int rule_output_index = rule_output_val + 3;
            if (rule_output_index < 0 || rule_output_index > 6) continue;
            float output_val = output_levels[rule_output_index];
            numerator += weight * output_val;
            denominator += weight;
        }
    }
    return (denominator == 0) ? 0.0f : numerator / denominator;
}


float FuzzyPIDControl::Fuzzy_PID_Control(float error) {
    this->err = error;
    float ec = this->err - this->olderr;
    this->last_ec = ec;

    delta_kp = 0; delta_ki = 0; delta_kd = 0;

    if (fuzzy_enabled) {
        float e_clamped = std::max(e_universe[0], std::min(e_universe[1], this->err));
        float ec_clamped = std::max(ec_universe[0], std::min(ec_universe[1], ec));
        std::vector<float> e_memberships(7);
        std::vector<float> ec_memberships(7);
        calculate_memberships(e_clamped, e_universe, e_memberships);
        calculate_memberships(ec_clamped, ec_universe, ec_memberships);
        delta_kp = get_output(e_memberships, ec_memberships, rule_kp, delta_kp_levels);
        delta_ki = get_output(e_memberships, ec_memberships, rule_ki, delta_ki_levels);
        delta_kd = get_output(e_memberships, ec_memberships, rule_kd, delta_kd_levels);
        
        // 针对大惯性船只的特殊处理
        float abs_error = std::abs(this->err);
        
        // 大误差时增强响应
        if (abs_error > 30.0f) {
            delta_kp = std::max(delta_kp, 0.5f);  // 确保有足够的比例增益
            delta_kd = std::min(delta_kd, -5.0f);  // 增强阻尼抑制超调
        }
        
        // 中等误差时平衡响应
        else if (abs_error > 10.0f) {
            // 保持当前调整
        }
        
        // 小误差时精确控制
        else if (abs_error > 2.0f) {
            delta_kp = std::min(delta_kp, 0.3f);  // 减少比例增益避免震荡
            delta_kd = std::max(delta_kd, -3.0f);  // 减少阻尼提高响应
        }
        
        // 接近目标时启用积分
        else {
            delta_ki = std::max(delta_ki, 0.001f);  // 启用积分消除稳态误差
        }
    }
    
    kp = kp_base + delta_kp;
    ki = ki_base + delta_ki;
    kd = kd_base + delta_kd;

    // 参数限制
    if (kp < 0.1) kp = 0.1;
    if (ki < 0) ki = 0;
    if (kd < 0) kd = 0;

    sumerr += this->err;
    
    // 针对大惯性船只的积分限制
    float max_integral = MaxSum * 0.8f;  // 限制积分项避免饱和
    if (sumerr > max_integral) sumerr = max_integral;
    if (sumerr < -max_integral) sumerr = -max_integral;

    // ===== [新增] 计算并存储各分项输出 =====
    p_out = kp * this->err;
    i_out = ki * sumerr;
    d_out = kd * ec;
    // ======================================
    
    float control = p_out + i_out + d_out;

    if (control > MaxOutput) control = MaxOutput;
    if (control < MinOutput) control = MinOutput;
    
    this->olderr = this->err;
    return control;
}