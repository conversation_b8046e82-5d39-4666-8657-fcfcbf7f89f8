#ifndef _MOTION_PLAN_H
#define _MOTION_PLAN_H

#include <string.h>
#include <termios.h>
#include <vector>

#include "../../include/Typedef.h"
#include "../../include/boat_datadef.h"

using namespace std;

/**
 * @file motion_plan.h
 * @brief 运动规划类定义
 * @description 实现无人船的路径规划和轨迹跟踪功能
 */

/**
 * @brief 轨迹跟踪参数结构体
 * @description 存储轨迹跟踪算法中使用的所有参数和状态变量
 */
typedef struct
{
	int DotNum;        // 总航点数量
	int TrackNum;      // 当前正在跟踪的航点序号
	int ReturnFlag;    // 返航标志：0=正常跟踪，1=任务完成准备返航
	int TrackFlag;     // 跟踪状态标志：0=航向目标点，1=在航点间轨迹跟踪
	int FirstFlag;     // 首次执行标志：0=首次，1=已经开始跟踪
	
	float deltat;      // 控制周期时间间隔 (s)
	float setVel;      // 设定的目标速度 (m/s)
	float eta;         // 轨迹跟踪增益参数（影响收敛速度）
	float switchlen;   // 航点切换距离阈值 (m)
	float maxSpeed;    // 最大允许速度 (m/s)
	float firstphi;    // 首次计算的目标航向角 (度)
	float FirstDotAngle; // 第一个航点的角度 (度)

	float TrajectoryUnit[50][2];  // 航迹段的单位方向向量数组 [段数][x,y]
	float TargetPos[2];           // 当前目标位置 [x, y]
}TrackingPlanParam;

/**
 * @class MotionPlan
 * @brief 运动规划主类
 * @description 负责无人船的航迹规划、轨迹跟踪和任务管理
 * 
 * 主要功能：
 * - 航迹点解析：从任务指令中提取有效的航行点
 * - 轨迹规划：计算航点间的连接轨迹和方向向量
 * - 轨迹跟踪：实现平滑的航点跟踪和航段切换
 * - 任务管理：处理任务状态和完成判断
 */
class MotionPlan
{
public:
	/**
	 * @brief 运动规划器构造函数
	 * @description 初始化所有参数和状态变量
	 */
	MotionPlan();
	
	// ================== 任务数据 ==================
	anchor_mission_control    tracking_task;  // 当前执行的跟踪任务

	// ================== 控制指令数据 ==================
	flash_model_data actionModelData;                    // 动作模式数据
	actuator_control actionActuatorControl;              // 执行器直接控制指令
	headvel_control  actionHeadvelControl;               // 航向速度控制指令
	trajectory_mission_control actionTrajectoryMissionControl; // 轨迹任务控制指令
	
	// ================== 算法状态变量 ==================
	int _precmd;  // 前一个指令类型（用于指令切换检测）

	// ================== 轨迹跟踪参数 ==================
	TrackingPlanParam  dotTrackingParam;   // 点对点跟踪参数
	TrackingPlanParam  lineTrackingParam;  // 直线轨迹跟踪参数（预留）

	// ================== 轨迹数据 ==================
	float trackline[50][2];  // 航迹线段数据：存储航点间的轨迹线段
	float lineunit[50][2];   // 航迹线段的单位方向向量

	// ================== 全局状态管理 ==================
	bool globalFirstFlag;    // 全局首次执行标志
	double globalMapPos[2];  // 全局地图位置（预留用于地图更新）

	// ================== 等待控制参数 ==================
	int WaitTime;     // 在当前航点的等待时间 (单位：控制周期数)
	int WaitTimeNum;  // 等待时间计数器
	int WaitFlag;     // 等待状态标志：0=正常航行，1=在航点等待
	
	// ================== 航向控制精度评估参数 ==================
	float headingHistory[10];  // 存储10次航向数据的数组
	float targetHeading;       // 目标航向角
	int headingDataCount;      // 当前采集的航向数据数量
	int taskRunningTime;       // 任务运行时间计数器(秒)
	int lastSampleTime;        // 上次采样的时间(秒)
	bool headingEvalStarted;   // 航向评估启动标志
	bool headingEvalInProcess; // 航向评估过程标志
	
	// // ================== 航迹跟踪精度评估参数 ==================
	// struct {
	// 	float idealPoints[10][2];  // 理想航迹上的10个均匀分布点 [x, y]
	// 	float actualPoints[10][2]; // 实际航迹上对应的最近点 [x, y]
	// 	int pointCount;            // 当前采集的点数
	// 	bool evalStarted;          // 航迹评估启动标志
	// 	bool evalInProcess;        // 航迹评估过程标志
	// 	float segmentStart[2];     // 航段起点坐标
	// 	float segmentEnd[2];       // 航段终点坐标
	// 	float segmentLength;       // 航段长度
	// 	int evalStep;             // 评估步骤计数器
	// } trackAccuracyEval;

	// ================== 航迹跟踪精度评估参数 (新版) ==================
	struct TrackAccuracyEval {
		float idealPoints[10][2];      // 理想航迹上的10个均匀分布点

		std::vector<std::pair<float, float>> actualTrackHistory; // 【新增】用于存储实际航迹历史

		bool evalStarted;              // 航迹评估启动标志
		float segmentStart[2];         // 航段起点坐标
		float segmentEnd[2];           // 航段终点坐标
		float segmentLength;           // 航段长度
	} trackAccuracyEval;

	// ================== LOS导航和局部避障参数 ==================
	struct LOSNavigation {
		float lookaheadDistance;      // LOS前视距离 (m)
		float acceptanceRadius;       // 航点接受半径 (m)
		float currentLOSAngle;        // 当前LOS角度 (度)
		bool useLocalObstacleAvoidance; // 是否启用局部避障

		// 局部避障参数
		float obstacleDetectionRange; // 障碍物检测范围 (m)
		float avoidanceGain;          // 避障增益
		float safetyMargin;           // 安全边距 (m)
		float maxAvoidanceAngle;      // 最大避障角度 (度)

		// 地图相关参数
		float mapResolution;          // 地图分辨率 (m/pixel)
		float mapOriginX, mapOriginY; // 地图原点坐标 (m)
		int mapWidth, mapHeight;      // 地图尺寸 (pixels)
	} losNavigation;
	
	// ================== 任务阶段管理 ==================
	enum TaskPhase {
		PHASE_HEADING_CONTROL,    // 航向控制阶段（起点到第一个航点）
		PHASE_TRACK_ACCURACY,     // 航迹跟踪精度评估阶段（第一个到第二个航点）
		PHASE_NORMAL_TRACKING     // 正常轨迹跟踪阶段
	};
	TaskPhase currentPhase;      // 当前任务阶段
	bool phaseTransitionFlag;    // 阶段转换标志
	
	/**
	 * @brief 航向控制精度评估函数
	 * @param currentHeading 当前航向角
	 * @param targetHeading 目标航向角
	 * @description 基于比赛要求，评估航向控制精度
	 */
	void EvaluateHeadingAccuracy(float currentHeading, float targetHeading);
	
	/**
	 * @brief 计算并输出航向控制精度
	 * @description 根据采集的10个航向数据计算控制精度并输出结果
	 */
	void CalculateHeadingAccuracy();

	/**
	 * @brief 初始化航迹跟踪精度评估
	 * @param startPoint 航段起点坐标
	 * @param endPoint 航段终点坐标
	 * @description 在第一个航点到达时调用，初始化航迹跟踪精度评估参数
	 */
	void InitTrackAccuracyEvaluation(float startPoint[2], float endPoint[2]);
	
	/**
	 * @brief 航迹跟踪精度评估函数
	 * @param currentPos 当前位置坐标
	 * @description 在第一个到第二个航点之间持续调用，评估航迹跟踪精度
	 */
	void EvaluateTrackAccuracy(float currentPos[2]);
	
	/**
	 * @brief 计算并输出航迹跟踪精度
	 * @description 根据采集的10个点计算航迹跟踪精度并输出结果
	 */
	void CalculateTrackAccuracy();

	/**
	 * @brief 全局地图更新函数（预留接口）
	 * @param boatstate 当前船舶状态
	 * @param localmap 局部地图数据
	 * @description 用于未来集成SLAM或地图更新功能
	 */
	void GlobalMapFlash(TransState boatstate, vector<vector<int> >localmap);

	/**
	 * @brief 航迹获取和预处理函数
	 * @param boatstate 当前船舶状态
	 * @param target 输入的任务指令
	 * @description 关键函数：解析任务指令，提取航点，计算轨迹参数
	 * 
	 * 功能详述：
	 * 1. 过滤任务指令，提取类型为16的航点指令
	 * 2. 计算航点间的方向向量和距离
	 * 3. 初始化轨迹跟踪参数
	 * 4. 设置首个目标点和跟踪参数
	 */
	void TrajecotryGet(TransState boatstate, anchor_mission_control target);

	/**
	 * @brief 轨迹跟踪控制函数
	 * @param boatstate 当前船舶状态
	 * @description 核心算法函数：实现实时的轨迹跟踪控制
	 *
	 * 算法特点：
	 * 1. 两阶段跟踪：先航向目标点，再进行轨迹跟踪
	 * 2. 智能航点切换：基于距离和角度的双重判断
	 * 3. 转弯优化：大角度转弯时的特殊处理
	 * 4. 任务完成检测：自动检测并处理任务结束
	 *
	 * 控制流程：
	 * - TrackFlag=0: 航向目标点阶段，计算目标航向角
	 * - TrackFlag=1: 轨迹跟踪阶段，进行精确的路径跟踪
	 * - ReturnFlag=1: 任务完成，停止控制输出
	 */
	void TrajectoryTrackingDirect(TransState boatstate);

	/**
	 * @brief LOS导航算法
	 * @param currentPos 当前位置 [x, y]
	 * @param targetPos 目标位置 [x, y]
	 * @param currentHeading 当前航向角 (度)
	 * @return 计算得到的LOS角度 (度)
	 * @description 实现Line of Sight导航算法，计算朝向目标点的理想航向
	 */
	float CalculateLOSAngle(float currentPos[2], float targetPos[2], float currentHeading);

	/**
	 * @brief 计算点到直线的最近点和距离
	 * @param point 点坐标 [x, y]
	 * @param lineStart 直线起点 [x, y]
	 * @param lineEnd 直线终点 [x, y]
	 * @param closestPoint 输出最近点坐标 [x, y]
	 * @param distance 输出距离
	 * @return 是否成功计算
	 */
	bool PointToLineDistance(float point[2], float lineStart[2], float lineEnd[2], float closestPoint[2], float& distance);

	/**
	 * @brief 计算航迹线上的前视点
	 * @param currentPos 当前位置 [x, y]
	 * @param lineStart 航迹线起点 [x, y]
	 * @param lineEnd 航迹线终点 [x, y]
	 * @param lookaheadDist 前视距离 (m)
	 * @param lookaheadPoint 输出前视点坐标 [x, y]
	 * @return 是否成功计算前视点
	 */
	bool CalculateLookaheadPoint(float currentPos[2], float lineStart[2], float lineEnd[2], float lookaheadDist, float lookaheadPoint[2]);

	/**
	 * @brief LOS导航算法
	 * @param currentPos 当前位置 [x, y]
	 * @param targetPos 目标位置 [x, y]
	 * @param currentHeading 当前航向角 (度)
	 * @return 计算得到的LOS角度 (度)
	 * @description 实现Line of Sight导航算法，计算朝向目标点的理想航向
	 */
	float CalculateLOSAngle(float currentPos[2], float targetPos[2], float currentHeading);

	/**
	 * @brief 局部避障算法
	 * @param currentPos 当前位置 [x, y]
	 * @param desiredHeading 期望航向角 (度)
	 * @param localMap 局部地图数据
	 * @return 避障后的航向角 (度)
	 * @description 基于局部地图进行障碍物检测和避障路径规划
	 */
	float LocalObstacleAvoidance(float currentPos[2], float desiredHeading, vector<vector<int>>& localMap);

	/**
	 * @brief 检测指定方向上的障碍物
	 * @param currentPos 当前位置 [x, y]
	 * @param heading 检测方向 (度)
	 * @param range 检测距离 (m)
	 * @param localMap 局部地图数据
	 * @return 障碍物距离，如果没有障碍物返回-1
	 */
	float DetectObstacle(float currentPos[2], float heading, float range, vector<vector<int>>& localMap);

	/**
	 * @brief 世界坐标转换为地图像素坐标
	 * @param worldPos 世界坐标 [x, y]
	 * @param pixelPos 输出的像素坐标 [u, v]
	 * @return 转换是否成功
	 */
	bool WorldToPixel(float worldPos[2], int pixelPos[2]);

	/**
	 * @brief 像素坐标转换为世界坐标
	 * @param pixelPos 像素坐标 [u, v]
	 * @param worldPos 输出的世界坐标 [x, y]
	 * @return 转换是否成功
	 */
	bool PixelToWorld(int pixelPos[2], float worldPos[2]);

	/**
	 * @brief 输出调试信息到文件
	 * @param message 调试信息
	 * @description 将调试信息输出到tiaoshilog目录下的文件中
	 */
	void LogDebugInfo(const std::string& message);

};

#endif



// #ifndef _MOTION_PLAN_H
// #define _MOTION_PLAN_H

// #include <string.h>
// #include <termios.h>
// #include <vector>

// #include "../../include/Typedef.h"
// #include "../../include/boat_datadef.h"

// using namespace std;

// typedef struct
// {
// 	int DotNum;
// 	int TrackNum;
// 	int ReturnFlag;
// 	int TrackFlag;
// 	int FirstFlag;
// 	float deltat;
// 	float setVel;
// 	float eta;
// 	float switchlen;
// 	float maxSpeed;
// 	float firstphi;
// 	float FirstDotAngle;

// 	float TrajectoryUnit[50][2];
// 	float TargetPos[2];
// }TrackingPlanParam;


// class MotionPlan
// {
// public:
// 	MotionPlan();
// 	anchor_mission_control    tracking_task;

// 	flash_model_data actionModelData;

// 	actuator_control actionActuatorControl;
// 	headvel_control  actionHeadvelControl;
// 	trajectory_mission_control actionTrajectoryMissionControl;
// 	int _precmd = 0;
// 	TrackingPlanParam  dotTrackingParam;
// 	TrackingPlanParam  lineTrackingParam;

// 	float trackline[50][2];
// 	float lineunit[50][2];

// 	bool globalFirstFlag;

// 	double globalMapPos[2];

// 	int WaitTime, WaitTimeNum, WaitFlag;

// 	void GlobalMapFlash(TransState boatstate, vector<vector<int> >localmap);

// 	void TrajecotryGet(TransState boatstate, anchor_mission_control target);

// 	void TrajectoryTrackingDirect(TransState boatstate);

// };

// #endif
