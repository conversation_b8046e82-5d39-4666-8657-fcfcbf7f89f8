#include "motion_plan.h"
#include "math.h"
#include <stdio.h>
#include <iostream>
#include <fstream>
#include <chrono>
#include <ctime>

/**
 * @file motion_plan.cpp
 * @brief 运动规划实现文件
 * @description 实现无人船的航迹规划和轨迹跟踪算法
 */

/**
 * @brief 运动规划器构造函数
 * @description 初始化运动规划器的所有参数和状态变量
 * 
 * 初始化内容：
 * 1. 轨迹跟踪参数设置
 * 2. 等待控制参数清零
 * 3. 全局状态标志初始化
 * 4. 各种控制指令结构体清零
 */
MotionPlan::MotionPlan()
{
	// ================== 初始化轨迹跟踪参数 ==================
	memset(&dotTrackingParam, 0, sizeof(TrackingPlanParam));
	dotTrackingParam.TrackNum = 0;        // 当前跟踪航点序号
	dotTrackingParam.FirstDotAngle = 0;   // 首个航点角度
	dotTrackingParam.eta = 0.1;           // 轨迹跟踪增益（越大收敛越快但可能振荡）
	dotTrackingParam.switchlen = 20;      // 航点切换距离阈值 (m)
	dotTrackingParam.maxSpeed = 2.5;      // 最大允许速度 (m/s)
	dotTrackingParam.deltat = 0.1;        // 控制周期 (s)

	// ================== 初始化等待控制参数 ==================
	WaitTime = 0;      // 等待时间
	WaitTimeNum = 0;   // 等待计数器
	WaitFlag = 0;      // 等待标志

	// ================== 初始化全局状态 ==================
	globalFirstFlag = true;  // 全局首次执行标志
	_precmd = 0;             // 初始化前一个指令类型

	// ================== 清零所有控制数据结构 ==================
	memset(&tracking_task, 0, sizeof(anchor_mission_control));
	memset(&actionModelData, 0, sizeof(flash_model_data));
	memset(&actionActuatorControl, 0, sizeof(actuator_control));
	memset(&actionHeadvelControl, 0, sizeof(actuator_control));
	memset(&actionTrajectoryMissionControl, 0, sizeof(trajectory_mission_control));

	// ================== 清零轨迹数据 ==================
	memset(&trackline, 0, sizeof(trackline));
	memset(&lineunit, 0, sizeof(lineunit));

	// ================== 初始化LOS导航参数 ==================
	losNavigation.lookaheadDistance = 50.0f;      // LOS前视距离 50m
	losNavigation.acceptanceRadius = 20.0f;       // 航点接受半径 20m
	losNavigation.currentLOSAngle = 0.0f;         // 当前LOS角度
	losNavigation.useLocalObstacleAvoidance = true; // 启用局部避障

	// 局部避障参数
	losNavigation.obstacleDetectionRange = 100.0f; // 障碍物检测范围 100m
	losNavigation.avoidanceGain = 1.5f;           // 避障增益
	losNavigation.safetyMargin = 15.0f;           // 安全边距 15m
	losNavigation.maxAvoidanceAngle = 45.0f;      // 最大避障角度 45度

	// 地图相关参数
	losNavigation.mapResolution = 1.0f;           // 地图分辨率 1m/pixel
	losNavigation.mapOriginX = 0.0f;              // 地图原点X坐标
	losNavigation.mapOriginY = 0.0f;              // 地图原点Y坐标
	losNavigation.mapWidth = 800;                 // 地图宽度 800像素
	losNavigation.mapHeight = 800;                // 地图高度 800像素

	printf("LOS导航和局部避障系统已初始化\n");
	
	// ================== 初始化航向控制精度评估参数 ==================
	memset(headingHistory, 0, sizeof(headingHistory));
	targetHeading = 0.0f;
	headingDataCount = 0;
	taskRunningTime = 0;
	lastSampleTime = 0;
	headingEvalStarted = false;
	headingEvalInProcess = false;
	
	// ================== 初始化航迹跟踪精度评估参数 ==================
trackAccuracyEval.evalStarted =false;
trackAccuracyEval.actualTrackHistory.clear();
	
	// ================== 初始化任务阶段管理 ==================
	currentPhase = PHASE_HEADING_CONTROL;  // 初始阶段为航向控制
	phaseTransitionFlag = false;
}

/**
 * @brief 航向控制精度评估函数
 * @param currentHeading 当前航向角
 * @param targetHeading 目标航向角
 * @description 根据比赛要求进行航向控制精度评估
 *              任务运行10s后，每隔1s采集一次航向数据，连续采集10次
 *              然后计算这10次航向与目标航向的均方根误差
 */
void MotionPlan::EvaluateHeadingAccuracy(float currentHeading, float targetHeading)
{
	// 更新任务运行时间（假设每次调用这个函数间隔为0.1秒，即控制周期）
	taskRunningTime += 1;
	
	// 每10个控制周期等于1秒
	int currentTimeInSeconds = taskRunningTime / 10;
	
	// 任务运行10秒后开始评估
	if (currentTimeInSeconds >= 10 && !headingEvalStarted) {
		headingEvalStarted = true;
		this->targetHeading = targetHeading; // 保存目标航向
		printf("开始航向控制精度评估，目标航向: %.2f°\n", targetHeading);
	}
	
	// 开始评估过程
	if (headingEvalStarted) {
		// 每隔1秒采集一次航向数据
		if (currentTimeInSeconds > lastSampleTime) {
			lastSampleTime = currentTimeInSeconds;
			
			if (headingDataCount < 10) {
				// 记录当前航向数据
				headingHistory[headingDataCount] = currentHeading;
				headingDataCount++;
				printf("采集航向数据 #%d: %.2f° (目标航向: %.2f°)\n", 
					headingDataCount, currentHeading, targetHeading);
				
				// 开始航向评估过程
				headingEvalInProcess = true;
			}
			
			// 采集满10个数据后计算精度
			if (headingDataCount == 10 && headingEvalInProcess) {
				CalculateHeadingAccuracy();
				// 重置采集，准备下一轮评估（每10秒一次）
				headingDataCount = 0;
				headingEvalInProcess = false;
			}
		}
	}
}

/**
 * @brief 计算并输出航向控制精度
 * @description 根据采集的10个航向数据计算控制精度
 *              使用公式: h_e = sqrt(∑(h_i - h_0)^2 / 10)
 */
void MotionPlan::CalculateHeadingAccuracy()
{
	float squareSum = 0.0f;
	
	// 计算航向偏差的平方和
	for (int i = 0; i < 10; i++) {
		// 需要处理航向角跨越360度边界的情况
		float diff = headingHistory[i] - targetHeading;
		if (diff > 180.0f) diff -= 360.0f;
		if (diff < -180.0f) diff += 360.0f;
		
		squareSum += diff * diff;
	}
	
	// 计算均方根误差
	float headingAccuracy = sqrt(squareSum / 10.0f);
	
	// 输出结果
	printf("\n=====================================================\n");
	printf("航向控制精度评估结果（任务时间：%d 秒）\n", taskRunningTime / 10);
	printf("目标航向: %.2f°\n", targetHeading);
	printf("10次航向数据: ");
	for (int i = 0; i < 10; i++) {
		printf("%.2f° ", headingHistory[i]);
	}
	printf("\n航向控制精度: %.2f°\n", headingAccuracy);
	
	// 根据比赛要求判定分数
	if (headingAccuracy <= 2.0f) {
		printf("航向控制精度合格（≤2°）\n");
	} else {
		printf("航向控制精度不合格（>2°），得分为0\n");
	}
	printf("=====================================================\n\n");
}

/**
 * @brief (新版) 初始化航迹跟踪精度评估
 * @description 在到达第一个航点时调用。只负责生成理想点，并清空历史轨迹。
 */
void MotionPlan::InitTrackAccuracyEvaluation(float startPoint[2], float endPoint[2])
{
	// 1. 保存航段起点和终点
	trackAccuracyEval.segmentStart[0] = startPoint[0];
	trackAccuracyEval.segmentStart[1] = startPoint[1];
	trackAccuracyEval.segmentEnd[0] = endPoint[0];
	trackAccuracyEval.segmentEnd[1] = endPoint[1];
	
	// 2. 计算航段长度
	float dx = endPoint[0] - startPoint[0];
	float dy = endPoint[1] - startPoint[1];
	trackAccuracyEval.segmentLength = sqrt(dx*dx + dy*dy);
	
	// 3. 计算理想航迹上的10个均匀分布点（在后半段）
	float midPoint[2] = {
		(startPoint[0] + endPoint[0]) / 2.0f,
		(startPoint[1] + endPoint[1]) / 2.0f
	};
	for (int i = 0; i < 10; i++) {
		float ratio = (float)i / 9.0f; // 0到1的均匀分布
		trackAccuracyEval.idealPoints[i][0] = midPoint[0] + ratio * (endPoint[0] - midPoint[0]);
		trackAccuracyEval.idealPoints[i][1] = midPoint[1] + ratio * (endPoint[1] - midPoint[1]);
	}
	
	// 4. 清空历史轨迹记录，并启动评估标志
	trackAccuracyEval.actualTrackHistory.clear();
	trackAccuracyEval.actualTrackHistory.reserve(500); // 预分配一些空间以提高效率
	trackAccuracyEval.evalStarted = true;
	
	printf("=====================================================\n");
	printf("新版航迹跟踪精度评估已初始化\n");
	printf("航段起点: (%.2f, %.2f), 终点: (%.2f, %.2f)\n", startPoint[0], startPoint[1], endPoint[0], endPoint[1]);
	printf("=====================================================\n\n");
}

/**
 * @brief (新版) 航迹跟踪精度评估 - 过程函数
 * @description 在第一个到第二个航点之间持续调用。只负责记录当前位置。
 */
void MotionPlan::EvaluateTrackAccuracy(float currentPos[2])
{
	if (!trackAccuracyEval.evalStarted) {
		return; // 评估尚未开始
	}
	
	// 1. 持续记录当前船只位置到历史轨迹中
	trackAccuracyEval.actualTrackHistory.push_back(std::make_pair(currentPos[0], currentPos[1]));

	// 2. 检查是否到达第二个航点（评估结束和触发计算的条件）
	float dx = currentPos[0] - trackAccuracyEval.segmentEnd[0];
	float dy = currentPos[1] - trackAccuracyEval.segmentEnd[1];
	float distanceToEnd = sqrt(dx*dx + dy*dy);
	
	if (distanceToEnd <= 5.0f) { // 到达第二个航点
		// 确保只计算一次
		if (trackAccuracyEval.evalStarted) {
			CalculateTrackAccuracy(); // 触发最终计算
			trackAccuracyEval.evalStarted = false; // 关闭评估，防止重复计算
			printf("已到达第二个航点，航迹跟踪精度评估完成。\n");
		}
	}
}

/**
 * @brief (新版) 计算并输出航迹跟踪精度
 * @description 在到达第二个航点时被触发。严格按照题目要求进行计算。
 */
void MotionPlan::CalculateTrackAccuracy()
{
	if (trackAccuracyEval.actualTrackHistory.empty()) {
		printf("警告：实际航迹历史为空，无法计算精度。\n");
		return;
	}

	float squareSum = 0.0f;
	float paired_actual_points[10][2] = {0}; // 用于存储配对好的实际点，方便打印
    int valid_pairs = 0;

	// 1. 对于【每一个理想点】，都去【完整的实际航迹历史】中寻找最近点
	for (int i = 0; i < 10; i++) 
    {
		float min_dist_sq = -1.0f;
		int best_match_index = -1;
		
        // 遍历所有记录下来的实际航迹点
		for (size_t j = 0; j < trackAccuracyEval.actualTrackHistory.size(); j++) 
        {
			float dx = trackAccuracyEval.actualTrackHistory[j].first - trackAccuracyEval.idealPoints[i][0];
			float dy = trackAccuracyEval.actualTrackHistory[j].second - trackAccuracyEval.idealPoints[i][1];
			float dist_sq = dx*dx + dy*dy; // 使用距离的平方进行比较，避免开方运算，效率更高
			
			if (best_match_index == -1 || dist_sq < min_dist_sq) {
				min_dist_sq = dist_sq;
				best_match_index = j;
			}
		}

        // 如果找到了最近的匹配点
        if (best_match_index != -1)
        {
            squareSum += min_dist_sq; // 直接累加距离的平方
            valid_pairs++;
            // (可选) 记录下这个配对，用于后续打印
            paired_actual_points[i][0] = trackAccuracyEval.actualTrackHistory[best_match_index].first;
            paired_actual_points[i][1] = trackAccuracyEval.actualTrackHistory[best_match_index].second;
        }
	}
	
	if (valid_pairs == 0) {
		printf("警告：未能成功配对任何数据点。\n");
		return;
	}
	
	// 2. 计算均方根误差
	float trackAccuracy = sqrt(squareSum / valid_pairs);
	
	// 3. 输出结果
	printf("\n=====================================================\n");
	printf("新版航迹跟踪精度评估结果\n");
    printf("共记录了 %zu 个实际航迹点，成功配对 %d/10 个理想点\n", trackAccuracyEval.actualTrackHistory.size(), valid_pairs);
	printf("航迹跟踪精度 (RMSE): %.4f 米\n", trackAccuracy);
	
	// 根据比赛要求判定分数
	float vesselLength = 80.0f; // 假设船长
	if (trackAccuracy <= 0.5f * vesselLength) {
		printf("航迹跟踪精度合格（≤%.1f米）\n", 0.5f * vesselLength);
	} else {
		printf("航迹跟踪精度不合格（>%.1f米），得分为0\n", 0.5f * vesselLength);
	}
	printf("=====================================================\n\n");
}


/**
 * @brief 航迹获取和预处理函数
 * @param boatstate 当前船舶状态信息
 * @param task 输入的任务指令集
 * @description 解析任务指令，提取有效航点，计算轨迹参数
 * 
 * 算法流程：
 * 1. 过滤任务指令，只保留command=16的航点导航指令
 * 2. 提取航点的位置、速度、等待时间等参数
 * 3. 计算相邻航点间的方向向量（用于后续轨迹跟踪）
 * 4. 初始化轨迹跟踪的各种参数和状态
 * 
 * 重要参数说明：
 * - param1: 在该航点的等待时间 或 速度参数
 * - param3: 设定的航行速度
 * - lat/lon: 航点的经纬度坐标
 */
void MotionPlan::TrajecotryGet(TransState boatstate, anchor_mission_control task)
{
	// 清空并重新初始化轨迹跟踪参数
	memset(&dotTrackingParam, 0, sizeof(TrackingPlanParam));

	// ================== 初始化变量 ==================
	double nowpos[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
	homepos[0] = boatstate.HomePos[0];  // 获取家点纬度
	homepos[1] = boatstate.HomePos[1];  // 获取家点经度
	unsigned int seq_num = 0;           // 有效航点计数器
	unsigned int precommand = 0;        // 当前处理的指令类型

	// ================== 过滤和提取有效航点 ==================
	for (int i = 0; i < task.mission_num; i++)
	{
		precommand = task.mission[i].command;

		// 只处理command=16的航点导航指令
		if ((precommand == 16))
		{
			_precmd = precommand;  // 记录指令类型
			
			// 复制航点数据到跟踪任务结构
			tracking_task.mission[seq_num].command = task.mission[i].command;
			tracking_task.mission[seq_num].seq = seq_num;
			tracking_task.mission[seq_num].lat = task.mission[i].lat;  // 纬度坐标
			tracking_task.mission[seq_num].lon = task.mission[i].lon;  // 经度坐标
			tracking_task.mission[seq_num].alt = task.mission[i].alt;  // 高度/航向
			tracking_task.mission[seq_num].param1 = task.mission[i].param1;  // 等待时间
			tracking_task.mission[seq_num].param2 = task.mission[i].param2;  // 预留参数
			tracking_task.mission[seq_num].param3 = task.mission[i].param3;  // 设定速度
			tracking_task.mission[seq_num].param4 = task.mission[i].param4;  // 预留参数
			
			// 调试输出：显示解析的航点信息
			printf("trajectory seq: %d, x: %f, y:%f \n", tracking_task.mission[seq_num].seq, 
			       tracking_task.mission[seq_num].lon, tracking_task.mission[seq_num].lat);
			seq_num++;
		}
	}
	
	// ================== 设置任务基本信息 ==================
	tracking_task.mission_num = seq_num;  // 记录有效航点总数
	trackline[0][0] = 0;  // 初始化轨迹线数组
	trackline[0][1] = 0;

	// ================== 初始化轨迹跟踪参数 ==================
	dotTrackingParam.DotNum = tracking_task.mission_num;  // 航点总数
	dotTrackingParam.TrackNum = 0;        // 从第0个航点开始
	WaitTimeNum = 0;                      // 等待计数器清零
	WaitTime = tracking_task.mission[0].param1;  // 设置首个航点等待时间
	WaitFlag = 0;                         // 等待标志清零

	// ================== 设置速度参数 ==================
	dotTrackingParam.setVel = tracking_task.mission[0].param3;  // 从首个航点获取设定速度
	if (dotTrackingParam.setVel < 0)
	{
		dotTrackingParam.setVel = 0;  // 速度不能为负
	}
	
	// ================== 初始化状态标志 ==================
	dotTrackingParam.ReturnFlag = 0;   // 返航标志：0=正常任务执行
	dotTrackingParam.TrackFlag = 0;    // 跟踪标志：0=航向目标点阶段
	dotTrackingParam.FirstFlag = 0;    // 首次执行标志：0=首次
	
	// ================== 设置控制参数 ==================
	dotTrackingParam.eta = 0.1;        // 轨迹跟踪增益
	dotTrackingParam.switchlen = 50;   // 航点切换距离阈值 (m)
	dotTrackingParam.maxSpeed = 20;    // 最大允许速度 (m/s)
	dotTrackingParam.deltat = 0.1;     // 控制周期 (s)

	// ================== 计算航迹段方向向量 ==================
	// 为每一段航迹计算单位方向向量，用于后续的轨迹跟踪
	for (int i = 1; i < dotTrackingParam.DotNum; i++)
	{
		float errpos[2] = {0};  // 航迹段向量
		
		// 计算第i-1个航点到第i个航点的向量
		errpos[0] = tracking_task.mission[i].lat - tracking_task.mission[i - 1].lat;  // 纬度差
		errpos[1] = tracking_task.mission[i].lon - tracking_task.mission[i - 1].lon;  // 经度差
		
		// 计算向量长度（航迹段距离）
		float normerrpos = sqrtf(errpos[0] * errpos[0] + errpos[1] * errpos[1]);
		
		// 计算并存储单位方向向量
		dotTrackingParam.TrajectoryUnit[i - 1][0] = errpos[0] / normerrpos;  // X方向单位向量
		dotTrackingParam.TrajectoryUnit[i - 1][1] = errpos[1] / normerrpos;  // Y方向单位向量
	}
	
	// ================== 设置初始目标位置 ==================
	dotTrackingParam.TargetPos[0] = tracking_task.mission[0].lat;  // 首个目标点纬度
	dotTrackingParam.TargetPos[1] = tracking_task.mission[0].lon;  // 首个目标点经度
	
	// ================== 重置航向控制精度评估参数 ==================
	memset(headingHistory, 0, sizeof(headingHistory));
	headingDataCount = 0;
	taskRunningTime = 0;
	lastSampleTime = 0;
	headingEvalStarted = false;
	headingEvalInProcess = false;
	printf("任务开始，航向控制精度评估将在10秒后启动\n");
	
	// ================== 重置任务阶段管理 ==================
	currentPhase = PHASE_HEADING_CONTROL;  // 重置为航向控制阶段
	phaseTransitionFlag = false;

	// ================== 重置航迹跟踪精度评估参数 ==================
trackAccuracyEval.evalStarted =false;
trackAccuracyEval.actualTrackHistory.clear();

	printf("任务阶段管理已重置，当前阶段：航向控制\n");
}

/**
 * @brief 轨迹跟踪直接控制函数
 * @param boatstate 当前船舶状态信息
 * @param localMap 局部地图数据（可选，用于避障）
 * @description 核心算法函数：实现两阶段的轨迹跟踪控制
 *
 * 算法概述：
 * 这是无人船轨迹跟踪的核心算法，采用两阶段控制策略：
 * 1. 阶段1 (TrackFlag=0): 航向目标点阶段 - 直接朝向目标航点航行
 * 2. 阶段2 (TrackFlag=1): 轨迹跟踪阶段 - 精确跟踪航点间的轨迹
 *
 * 关键特性：
 * - 智能航点切换：基于距离和角度判断的双重切换逻辑
 * - 转弯优化：针对大角度转弯的特殊处理策略
 * - 任务管理：自动检测任务完成并处理结束状态
 * - 速度自适应：根据不同航点的速度参数动态调整
 * - 局部避障：基于局部地图的障碍物避障
 */
void MotionPlan::TrajectoryTrackingDirect(TransState boatstate, vector<vector<int>>* localMap)
{
	// ================== 变量初始化 ==================
	float lengthangle[2] = {0}, vel = 0;    // 长度角度和速度变量
	float pos[2]={0};                       // 当前位置 [x, y]
	float errpos[2] = {0};                  // 位置误差向量
	float normerrpos = 0;                   // 位置误差的模长
	float errfirstphi = 0;                  // 与初始角度的偏差
	float anglevel[2]={0};                  // 输出的角度和速度 [angle, velocity]
	float tarangle = 0;                     // 目标角度
	
	// 获取当前船舶相对于家点的位置
	pos[0] = boatstate.PostoHome[0];        // X坐标（相对家点）
	pos[1] = boatstate.PostoHome[1];        // Y坐标（相对家点）

	// ================== 阶段1：航向目标点控制 ==================
		if(dotTrackingParam.TrackFlag == 0)
		{
		// *** 计算当前位置到目标点的位置误差 ***
		errpos[0] = dotTrackingParam.TargetPos[0] - pos[0];  // X方向误差
		errpos[1] = dotTrackingParam.TargetPos[1] - pos[1];  // Y方向误差
		normerrpos = sqrtf(powf(errpos[0], 2) + powf(errpos[1], 2));  // 距离误差模长
		
		// *** 计算目标航向角 ***
		tarangle = atan2(errpos[0], errpos[1]) * 180.0/M_PI;  // 目标航向角（度）
				if (tarangle < 0)
				{
			tarangle += 360;  // 将角度归一化到[0, 360]度范围
				}

		// *** 局部避障处理 ***
		if (localMap != nullptr && losNavigation.useLocalObstacleAvoidance) {
			float avoidanceHeading = LocalObstacleAvoidance(pos, tarangle, *localMap);
			if (avoidanceHeading != tarangle) {
				// 记录避障信息
				char debugMsg[200];
				sprintf(debugMsg, "阶段1避障: 原航向=%.1f°, 避障航向=%.1f°, 位置=(%.1f,%.1f)",
				        tarangle, avoidanceHeading, pos[0], pos[1]);
				LogDebugInfo(debugMsg);
				tarangle = avoidanceHeading;
			}
		}
		
		// *** 首次执行时记录初始航向角 ***
				if(dotTrackingParam.FirstFlag == 0)
				{
			dotTrackingParam.FirstFlag = 1;        // 标记已经首次执行
			dotTrackingParam.firstphi = tarangle;  // 记录首次计算的航向角
				}
		
		// *** 航点切换判断1：距离判断 ***
				if(normerrpos<=5)
				{
			// 当距离目标点小于5米时，认为已到达航点，切换到下一个
			dotTrackingParam.TrackNum++;     // 航点序号递增
			dotTrackingParam.TrackFlag = 1;  // 切换到轨迹跟踪阶段
			WaitFlag = 1;                    // 设置等待标志
			WaitTime = tracking_task.mission[dotTrackingParam.TrackNum-1].param1;  // 设置等待时间
			
						if(dotTrackingParam.TrackNum < dotTrackingParam.DotNum)
						{
				// 还有下一个航点，设置下一航点的速度参数
								dotTrackingParam.setVel = tracking_task.mission[dotTrackingParam.TrackNum].param3;
				if(dotTrackingParam.setVel < 1) {dotTrackingParam.setVel = 1;}  // 最小速度限制
						}
						else
						{
				// 所有航点已完成，设置任务结束状态
				anglevel[0] = 0;                     // 停止转向
				anglevel[1] = 0;                     // 停止前进
				dotTrackingParam.ReturnFlag = 1;     // 设置返航标志
				dotTrackingParam.TrackNum = 0xff;    // 标记任务完成
						}
				}
		// *** 航点切换判断2：角度判断（转弯优化） ***
				else if (normerrpos<=20)
				{
			// 当距离在5-20米范围内时，检查角度变化
			errfirstphi = tarangle - dotTrackingParam.firstphi;  // 计算角度变化
			
			// 角度差归一化到[-180, 180]度范围
					if(errfirstphi > 180)
					{
							errfirstphi -= 360;
					}
					else if(errfirstphi < -180)
					{
							errfirstphi += 360;
					}
			
			// 如果角度变化超过90度，说明已经"过点"，应该切换航点
					if((errfirstphi > 90)||(errfirstphi < -90))
					{
				// 执行航点切换（同上面的距离判断逻辑）
						dotTrackingParam.TrackNum++;
						dotTrackingParam.TrackFlag = 1;
						WaitFlag = 1;
						WaitTime = tracking_task.mission[dotTrackingParam.TrackNum-1].param1;
				
						if(dotTrackingParam.TrackNum < dotTrackingParam.DotNum)
						{
								dotTrackingParam.setVel = tracking_task.mission[dotTrackingParam.TrackNum].param3;
								if(dotTrackingParam.setVel < 1) {dotTrackingParam.setVel = 1;}
						}
						else
						{
								anglevel[0] = 0;
								anglevel[1] = 0;
								dotTrackingParam.ReturnFlag = 1;
					dotTrackingParam.TrackNum = 0xff;
						}
					}
				}

		// *** 输出控制指令 ***
		vel = dotTrackingParam.setVel;  // 获取设定速度
				if (vel < 0)
			vel = 0;                    // 速度下限保护
				if (vel >= dotTrackingParam.setVel)
			vel = dotTrackingParam.setVel;  // 速度上限保护
		anglevel[0] = tarangle;         // 输出目标航向角
		anglevel[1] = vel;              // 输出目标速度
		}
	// ================== 阶段2：精确轨迹跟踪控制 ==================
		else if((dotTrackingParam.TrackFlag == 1)&&(dotTrackingParam.ReturnFlag == 0))
		{
		// *** 启用基于直线轨迹跟踪的LOS算法 ***
		// 该算法实现更精确的直线轨迹跟踪，通过Dot2Line函数计算
		// 船舶到航迹线的垂直距离和最近点，实现更高精度的路径跟踪
		float line[2][2] = {0};
		float pretovel[2] = {0};
		float tardot[2] = {0}, tardot2[2] = {0}, unit[2] = {0};

		// 设置当前航迹线段：从前一个航点到当前目标航点
		line[0][0] = tracking_task.mission[dotTrackingParam.TrackNum-1].lat;  // 起点X
		line[0][1] = tracking_task.mission[dotTrackingParam.TrackNum-1].lon;  // 起点Y
		line[1][0] = tracking_task.mission[dotTrackingParam.TrackNum].lat;    // 终点X
		line[1][1] = tracking_task.mission[dotTrackingParam.TrackNum].lon;    // 终点Y

		// 计算船舶到航迹线的最近点和单位方向向量
		Dot2Line(pos, line, tardot, unit);

		// 计算LOS前视点：在最近点基础上沿航迹线前进150米
		tardot2[0] = tardot[0] + unit[0] * 150;  // 前视点X坐标
		tardot2[1] = tardot[1] + unit[1] * 150;  // 前视点Y坐标

		// *** 使用LOS前视点计算位置误差 ***
		errpos[0] = tardot2[0] - pos[0];  // 朝向LOS前视点的X方向误差
		errpos[1] = tardot2[1] - pos[1];  // 朝向LOS前视点的Y方向误差

		// *** 航点切换判断：基于到目标航点的距离阈值 ***
		float distToTarget[2] = {tracking_task.mission[dotTrackingParam.TrackNum].lat - pos[0],
		                         tracking_task.mission[dotTrackingParam.TrackNum].lon - pos[1]};
		normerrpos = sqrtf(distToTarget[0]*distToTarget[0] + distToTarget[1]*distToTarget[1]);  // 计算到目标航点的距离
		if (normerrpos <= dotTrackingParam.switchlen)  // switchlen = 50米
				{
			// 距离小于切换阈值，切换到下一个航点
			dotTrackingParam.TrackNum++;     // 航点序号递增
			WaitFlag = 1;                    // 设置等待标志
			WaitTime = tracking_task.mission[dotTrackingParam.TrackNum-1].param1;  // 设置等待时间
			
						if(dotTrackingParam.TrackNum < (dotTrackingParam.DotNum))
						{
				// 还有下一个航点，更新速度参数
								dotTrackingParam.setVel = tracking_task.mission[dotTrackingParam.TrackNum].param3;
				if(dotTrackingParam.setVel < 1) {dotTrackingParam.setVel = 1;}  // 最小速度限制
						}
						else
						{
				// 所有航点已完成，设置任务结束状态
				anglevel[0] = 0;                     // 停止转向
				anglevel[1] = 0;                     // 停止前进  
				dotTrackingParam.ReturnFlag = 1;     // 设置返航标志
				dotTrackingParam.TrackNum = 0xff;    // 标记任务完成
						}
				}
		
		// *** 更新当前目标位置为LOS前视点 ***
				dotTrackingParam.TargetPos[0] = tardot2[0];  // 使用LOS前视点X坐标
				dotTrackingParam.TargetPos[1] = tardot2[1];  // 使用LOS前视点Y坐标

		// *** LOS轨迹跟踪控制算法 ***
		// 直接朝向LOS前视点，实现平滑的轨迹跟踪
		errpos[0] = tardot2[0] - pos[0];  // 朝向LOS前视点的X方向误差
		errpos[1] = tardot2[1] - pos[1];  // 朝向LOS前视点的Y方向误差
		normerrpos = sqrtf(powf(errpos[0], 2) + powf(errpos[1], 2));  // 误差模长

		// *** 计算LOS目标航向角 ***
		tarangle = atan2(errpos[0], errpos[1]) * 180.0/M_PI;  // 基于LOS前视点的目标航向
				if (tarangle < 0)
				{
			tarangle += 360;  // 角度归一化到[0, 360]度
				}

		// *** 局部避障处理 ***
		if (localMap != nullptr && losNavigation.useLocalObstacleAvoidance) {
			float avoidanceHeading = LocalObstacleAvoidance(pos, tarangle, *localMap);
			if (avoidanceHeading != tarangle) {
				// 记录避障信息
				char debugMsg[200];
				sprintf(debugMsg, "LOS避障: 原航向=%.1f°, 避障航向=%.1f°, 位置=(%.1f,%.1f), LOS点=(%.1f,%.1f)",
				        tarangle, avoidanceHeading, pos[0], pos[1], tardot2[0], tardot2[1]);
				LogDebugInfo(debugMsg);
				tarangle = avoidanceHeading;
			}
		}
		
		// *** 速度控制 ***
		// 可选：基于误差自适应速度 vel = normerrpos*dotTrackingParam.eta;
		vel = dotTrackingParam.setVel;  // 使用设定的固定速度
				if (vel < 0)
			vel = 0;                    // 速度下限保护
				if (vel >= dotTrackingParam.setVel)
			vel = dotTrackingParam.setVel;  // 速度上限保护
			
		// *** 输出控制指令 ***
		anglevel[0] = tarangle;         // 输出目标航向角
		anglevel[1] = vel;              // 输出目标速度
		}
	// ================== 阶段3：任务完成状态 ==================
		else if(dotTrackingParam.ReturnFlag == 1)
		{
		// 任务已完成，输出停止指令
			printf("finish!!!  direct control stop tracking  33333!\n");
		anglevel[0] = 0;  // 停止转向
		anglevel[1] = 0;  // 停止前进
		}
	
	// ================== 输出最终控制指令 ==================
	// 调试输出（可选）：显示当前的角度和速度指令
		// printf("angle:%f, vel:%f,\n", anglevel[0], anglevel[1]);
	
	// 设置控制模式和输出指令
	actionModelData.model = 10;                  // 设置为航向速度控制模式
	actionHeadvelControl.model = 2;              // 设置为闭环速度控制子模式
	actionHeadvelControl.heading = anglevel[0];  // 输出目标航向角（度）
	actionHeadvelControl.velocity = anglevel[1]; // 输出目标速度（m/s）
	
	// ================== 航向控制精度评估 ==================
	// 获取当前实际航向角（从boatstate中提取）
	float currentHeading = boatstate.VelEarth[3];  // 当前航向角
	
	// 根据任务阶段进行不同的评估
	if (currentPhase == PHASE_HEADING_CONTROL) {
		// 航向控制阶段：从起点到第一个航点
		EvaluateHeadingAccuracy(currentHeading, anglevel[0]);
		
		// 检查是否到达第一个航点，准备切换到航迹跟踪精度评估阶段
		// 当TrackNum从0变为1，且TrackFlag变为1时，表示到达第一个航点
		if (dotTrackingParam.TrackNum == 1 && dotTrackingParam.TrackFlag == 1 && !phaseTransitionFlag) {
			currentPhase = PHASE_TRACK_ACCURACY;
			phaseTransitionFlag = true;
			
			// 获取当前位置作为起点，第二个航点作为终点
			float startPoint[2] = {pos[0], pos[1]};  // 当前位置作为起点
			float endPoint[2] = {
				tracking_task.mission[1].lat,  // 第二个航点
				tracking_task.mission[1].lon
			};
			InitTrackAccuracyEvaluation(startPoint, endPoint);
			
			printf("阶段转换：从航向控制阶段切换到航迹跟踪精度评估阶段\n");
			printf("起点坐标: (%.2f, %.2f)\n", startPoint[0], startPoint[1]);
			printf("终点坐标: (%.2f, %.2f)\n", endPoint[0], endPoint[1]);
		}
	} else if (currentPhase == PHASE_TRACK_ACCURACY) {
		// 航迹跟踪精度评估阶段：第一个到第二个航点
		float currentPos[2] = {pos[0], pos[1]};
		EvaluateTrackAccuracy(currentPos);
		
		// 检查是否到达第二个航点，准备切换到正常轨迹跟踪阶段
		if (dotTrackingParam.TrackNum == 2 && !phaseTransitionFlag) {
			currentPhase = PHASE_NORMAL_TRACKING;
			phaseTransitionFlag = true;
			printf("阶段转换：从航迹跟踪精度评估阶段切换到正常轨迹跟踪阶段\n");
		}
	} else {
		// 正常轨迹跟踪阶段：不再进行特殊评估
		// 可以在这里添加其他评估功能
	}
}

/**
 * @brief 世界坐标转换为地图像素坐标
 * @param worldPos 世界坐标 [x, y]
 * @param pixelPos 输出的像素坐标 [u, v]
 * @return 转换是否成功
 */
bool MotionPlan::WorldToPixel(float worldPos[2], int pixelPos[2])
{
	// 假设地图中心对应船舶当前位置
	// 地图分辨率为1m/pixel，800x800像素
	int centerX = 400;  // 地图中心X像素坐标
	int centerY = 400;  // 地图中心Y像素坐标

	// 将世界坐标转换为相对于地图中心的像素偏移
	pixelPos[0] = centerX + (int)(worldPos[0] / losNavigation.mapResolution);
	pixelPos[1] = centerY - (int)(worldPos[1] / losNavigation.mapResolution);  // Y轴翻转

	// 检查是否在地图范围内
	if (pixelPos[0] >= 0 && pixelPos[0] < losNavigation.mapWidth &&
	    pixelPos[1] >= 0 && pixelPos[1] < losNavigation.mapHeight) {
		return true;
	}
	return false;
}

/**
 * @brief 像素坐标转换为世界坐标
 * @param pixelPos 像素坐标 [u, v]
 * @param worldPos 输出的世界坐标 [x, y]
 * @return 转换是否成功
 */
bool MotionPlan::PixelToWorld(int pixelPos[2], float worldPos[2])
{
	int centerX = 400;  // 地图中心X像素坐标
	int centerY = 400;  // 地图中心Y像素坐标

	// 将像素坐标转换为世界坐标
	worldPos[0] = (pixelPos[0] - centerX) * losNavigation.mapResolution;
	worldPos[1] = (centerY - pixelPos[1]) * losNavigation.mapResolution;  // Y轴翻转

	return true;
}

/**
 * @brief 检测指定方向上的障碍物
 * @param currentPos 当前位置 [x, y]
 * @param heading 检测方向 (度)
 * @param range 检测距离 (m)
 * @param localMap 局部地图数据
 * @return 障碍物距离，如果没有障碍物返回-1
 */
float MotionPlan::DetectObstacle(float currentPos[2], float heading, float range, vector<vector<int>>& localMap)
{
	// 将角度转换为弧度
	float headingRad = heading * M_PI / 180.0f;

	// 计算检测步长（每米检测一次）
	float stepSize = 1.0f;  // 1米步长
	int numSteps = (int)(range / stepSize);

	for (int i = 1; i <= numSteps; i++) {
		// 计算检测点的世界坐标
		float checkPos[2];
		checkPos[0] = currentPos[0] + i * stepSize * sin(headingRad);
		checkPos[1] = currentPos[1] + i * stepSize * cos(headingRad);

		// 转换为像素坐标
		int pixelPos[2];
		if (WorldToPixel(checkPos, pixelPos)) {
			// 检查该像素是否为障碍物
			if (localMap[pixelPos[1]][pixelPos[0]] == 1) {  // 注意：premap[i][j]中i是行(Y)，j是列(X)
				return i * stepSize;  // 返回障碍物距离
			}
		}
	}

	return -1;  // 没有检测到障碍物
}

/**
 * @brief 局部避障算法
 * @param currentPos 当前位置 [x, y]
 * @param desiredHeading 期望航向角 (度)
 * @param localMap 局部地图数据
 * @return 避障后的航向角 (度)
 */
float MotionPlan::LocalObstacleAvoidance(float currentPos[2], float desiredHeading, vector<vector<int>>& localMap)
{
	// 检测前方是否有障碍物
	float obstacleDistance = DetectObstacle(currentPos, desiredHeading, losNavigation.obstacleDetectionRange, localMap);

	// 如果前方没有障碍物或障碍物距离足够远，直接返回期望航向
	if (obstacleDistance < 0 || obstacleDistance > losNavigation.safetyMargin) {
		return desiredHeading;
	}

	// 前方有障碍物，需要避障
	float bestHeading = desiredHeading;
	float maxClearDistance = obstacleDistance;

	// 在期望航向左右搜索最佳避障方向
	for (float angle = 5.0f; angle <= losNavigation.maxAvoidanceAngle; angle += 5.0f) {
		// 检查左侧
		float leftHeading = desiredHeading - angle;
		if (leftHeading < 0) leftHeading += 360;
		float leftDistance = DetectObstacle(currentPos, leftHeading, losNavigation.obstacleDetectionRange, localMap);

		if (leftDistance < 0 || leftDistance > maxClearDistance) {
			bestHeading = leftHeading;
			maxClearDistance = (leftDistance < 0) ? losNavigation.obstacleDetectionRange : leftDistance;
			break;  // 找到可行方向，立即使用
		}

		// 检查右侧
		float rightHeading = desiredHeading + angle;
		if (rightHeading >= 360) rightHeading -= 360;
		float rightDistance = DetectObstacle(currentPos, rightHeading, losNavigation.obstacleDetectionRange, localMap);

		if (rightDistance < 0 || rightDistance > maxClearDistance) {
			bestHeading = rightHeading;
			maxClearDistance = (rightDistance < 0) ? losNavigation.obstacleDetectionRange : rightDistance;
			break;  // 找到可行方向，立即使用
		}
	}

	return bestHeading;
}

/**
 * @brief 输出调试信息到文件
 * @param message 调试信息
 */
void MotionPlan::LogDebugInfo(const std::string& message)
{
	// 创建调试日志文件路径
	static bool firstCall = true;
	static std::ofstream logFile;

	if (firstCall) {
		// 确保tiaoshilog目录存在
		system("mkdir -p tiaoshilog");

		// 打开日志文件
		logFile.open("tiaoshilog/navigation_debug.log", std::ios::app);
		firstCall = false;

		if (logFile.is_open()) {
			// 写入时间戳
			auto now = std::chrono::system_clock::now();
			auto time_t = std::chrono::system_clock::to_time_t(now);
			logFile << "\n=== Navigation Debug Log Started at " << std::ctime(&time_t) << "===\n";
		}
	}

	if (logFile.is_open()) {
		logFile << message << std::endl;
		logFile.flush();  // 立即写入文件
	}
}

