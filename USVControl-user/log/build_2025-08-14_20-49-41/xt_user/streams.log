[0.015s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.089s] [35m[1mConsolidate compiler generated dependencies of target planning_hpp[0m
[0.118s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[0.789s] [ 22%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[0.948s] [ 77%] Built target planning_hpp
[1.000s] [ 88%] [32m[1mLinking CXX executable xt_user_node[0m
[2.026s] [100%] Built target xt_user_node
[2.043s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[2.061s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[2.064s] -- Install configuration: ""
[2.064s] -- Execute custom install script
[2.065s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[2.065s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[2.066s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[2.066s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[2.066s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[2.066s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[2.066s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[2.066s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[2.067s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[2.067s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[2.067s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[2.067s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[2.067s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv
[2.081s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[2.081s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[2.081s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[2.081s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[2.081s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.xml
[2.084s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
