[0.106s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.106s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x793e15a1c7f0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x793e15a1c280>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x793e15a1c280>>)
[0.157s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.157s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.157s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.157s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.157s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.157s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.157s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/haique_work/usv_control/project2/USVControl-user'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.166s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ignore', 'ignore_ament_install']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore_ament_install'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_pkg']
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_pkg'
[0.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ros']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ros'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['cmake', 'python']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'cmake'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['python_setup_py']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python_setup_py'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ignore', 'ignore_ament_install']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore_ament_install'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_pkg']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_pkg'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_meta']
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_meta'
[0.169s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ignore', 'ignore_ament_install']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore_ament_install'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_pkg']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_pkg'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_meta']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_meta'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ros']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ros'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['cmake', 'python']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'cmake'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['python_setup_py']
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python_setup_py'
[0.170s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ros'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['cmake', 'python']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'cmake'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['python_setup_py']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python_setup_py'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ignore', 'ignore_ament_install']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore_ament_install'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_pkg']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_pkg'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_meta']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_meta'
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ros']
[0.171s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ignore', 'ignore_ament_install']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore_ament_install'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_pkg']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_pkg'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_meta']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_meta'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ros']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ros'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['cmake', 'python']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'cmake'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['python_setup_py']
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python_setup_py'
[0.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_meta']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_meta'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ros']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ros'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['cmake', 'python']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'cmake'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['python_setup_py']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python_setup_py'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ignore', 'ignore_ament_install']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore_ament_install'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_pkg']
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_pkg'
[0.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ros'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['cmake', 'python']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'cmake'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['python_setup_py']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python_setup_py'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_pkg'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_meta']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_meta'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ros']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ros'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['cmake', 'python']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'cmake'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['python_setup_py']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python_setup_py'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['ignore', 'ignore_ament_install']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ignore'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ignore_ament_install'
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['colcon_pkg']
[0.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ros'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['cmake', 'python']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'cmake'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'python'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['python_setup_py']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'python_setup_py'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['ignore', 'ignore_ament_install']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ignore'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ignore_ament_install'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['colcon_pkg']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'colcon_pkg'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['colcon_meta']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'colcon_meta'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['ros']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ros'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['cmake', 'python']
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'cmake'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'python'
[0.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['python_setup_py']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'python_setup_py'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ros'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['cmake', 'python']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'cmake'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'python'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['python_setup_py']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'python_setup_py'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'ros'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['cmake', 'python']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'cmake'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'python'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['python_setup_py']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'python_setup_py'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'ignore_ament_install'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['colcon_pkg']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'colcon_pkg'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['colcon_meta']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'colcon_meta'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['ros']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'ros'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['cmake', 'python']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'cmake'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'python'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['python_setup_py']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'python_setup_py'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['ignore', 'ignore_ament_install']
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'ignore'
[0.177s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'ignore_ament_install'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['colcon_pkg']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'colcon_pkg'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['colcon_meta']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'colcon_meta'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['ros']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'ros'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['cmake', 'python']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'cmake'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'python'
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['python_setup_py']
[0.178s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'python_setup_py'
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.178s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.198s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.199s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.201s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/haique_work/usv_control/project2/USVControl-user/install
[0.202s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 273 installed packages in /opt/ros/humble
[0.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.241s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.242s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.242s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.246s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.247s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.247s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning'
[0.249s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.249s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.249s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.260s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.416s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.421s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[0.448s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[0.453s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[0.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[0.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[0.455s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[0.456s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[0.456s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[0.458s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[0.458s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[0.458s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[0.459s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[0.459s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[0.460s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.461s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[0.461s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[0.462s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[0.463s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[0.463s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[0.466s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[0.466s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[0.467s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[0.467s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[0.468s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[0.468s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[0.469s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[0.469s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[0.469s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[0.470s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[0.470s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.471s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[0.472s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[0.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[0.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[0.474s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[0.474s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[0.475s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.475s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.478s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.478s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.482s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.482s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.482s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.496s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.496s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.ps1'
[0.497s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_ps1.py'
[0.498s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.ps1'
[0.499s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.sh'
[0.500s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_sh.py'
[0.500s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.sh'
[0.501s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.bash'
[0.502s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.bash'
[0.503s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.zsh'
[0.504s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.zsh'
