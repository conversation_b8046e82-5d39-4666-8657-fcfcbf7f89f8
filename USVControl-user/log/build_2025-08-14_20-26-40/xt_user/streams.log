[0.014s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.065s] [35m[1mConsolidate compiler generated dependencies of target planning_hpp[0m
[0.085s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[0.728s] [ 22%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[0.857s] [ 77%] Built target planning_hpp
[0.888s] [ 88%] [32m[1mLinking CXX executable xt_user_node[0m
[1.790s] [100%] Built target xt_user_node
[1.802s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[1.811s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[1.817s] -- Install configuration: ""
[1.817s] -- Execute custom install script
[1.818s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[1.819s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[1.819s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[1.819s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[1.819s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[1.820s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[1.821s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv
[1.830s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[1.830s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[1.831s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[1.831s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[1.831s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.xml
[1.833s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
