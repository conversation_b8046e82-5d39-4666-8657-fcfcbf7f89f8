[0.126s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.126s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7b834d908730>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7b834d9081c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7b834d9081c0>>)
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.175s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.176s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.176s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/haique_work/usv_control/project2/USVControl-user'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.176s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.179s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.180s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.181s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.182s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.185s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.186s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.187s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ignore', 'ignore_ament_install']
[0.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_pkg'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_meta']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_meta'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ros']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ros'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['cmake', 'python']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'cmake'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['python_setup_py']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python_setup_py'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ros'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['cmake', 'python']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'cmake'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['python_setup_py']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python_setup_py'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ros'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['cmake', 'python']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'cmake'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['python_setup_py']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python_setup_py'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore_ament_install'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_pkg']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_pkg'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_meta']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_meta'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ros']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ros'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['cmake', 'python']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'cmake'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['python_setup_py']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python_setup_py'
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ignore', 'ignore_ament_install']
[0.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ros'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['cmake', 'python']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'cmake'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['python_setup_py']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python_setup_py'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ignore', 'ignore_ament_install']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore_ament_install'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_pkg']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_pkg'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_meta']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_meta'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ros']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ros'
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['cmake', 'python']
[0.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'cmake'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['python_setup_py']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python_setup_py'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ignore', 'ignore_ament_install']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore_ament_install'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_pkg']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_pkg'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_meta']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_meta'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ros']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ros'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['cmake', 'python']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'cmake'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['python_setup_py']
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python_setup_py'
[0.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ros'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['cmake', 'python']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'cmake'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['python_setup_py']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python_setup_py'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ignore', 'ignore_ament_install']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore_ament_install'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_pkg']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_pkg'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_meta']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_meta'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ros']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ros'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['cmake', 'python']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'cmake'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['python_setup_py']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python_setup_py'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'ros'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['cmake', 'python']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'cmake'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'python'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['python_setup_py']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'python_setup_py'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'ros'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['cmake', 'python']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'cmake'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'python'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['python_setup_py']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'python_setup_py'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'ros'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['cmake', 'python']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'cmake'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'python'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['python_setup_py']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'python_setup_py'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'ros'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['cmake', 'python']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'cmake'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'python'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['python_setup_py']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'python_setup_py'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'python'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['python_setup_py']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'ros'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['cmake', 'python']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'cmake'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'python'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['python_setup_py']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'python_setup_py'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'ignore'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'ignore_ament_install'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['colcon_pkg']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'colcon_pkg'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['colcon_meta']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'colcon_meta'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['ros']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'ros'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['cmake', 'python']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'cmake'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'python'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['python_setup_py']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'python_setup_py'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'ros'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['cmake', 'python']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'cmake'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'python'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['python_setup_py']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'python_setup_py'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'ros'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['cmake', 'python']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'cmake'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'python'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['python_setup_py']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'python_setup_py'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'ros'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['cmake', 'python']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'cmake'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'python'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['python_setup_py']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'python_setup_py'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'ros'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['cmake', 'python']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'cmake'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'python'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['python_setup_py']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'python_setup_py'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'ros'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['cmake', 'python']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'cmake'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'python'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['python_setup_py']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'python_setup_py'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'ros'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['cmake', 'python']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'cmake'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'python'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['python_setup_py']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'python_setup_py'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['colcon_pkg']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'colcon_pkg'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'ros'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['cmake', 'python']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'cmake'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'python'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['python_setup_py']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'python_setup_py'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'ros'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['cmake', 'python']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'cmake'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'python'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['python_setup_py']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'python_setup_py'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['cmake', 'python']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'cmake'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'python'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['python_setup_py']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'python_setup_py'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'ros'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['cmake', 'python']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'cmake'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'python'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['python_setup_py']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'python_setup_py'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'ros'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['cmake', 'python']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'cmake'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'python'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['python_setup_py']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'python_setup_py'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'ros'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['cmake', 'python']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'cmake'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'python'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['python_setup_py']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'python_setup_py'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'ros'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['cmake', 'python']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'cmake'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'python'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['python_setup_py']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'python_setup_py'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'ros'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['cmake', 'python']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'cmake'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'python'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['python_setup_py']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'python_setup_py'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'python'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['python_setup_py']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'python_setup_py'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'python'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['python_setup_py']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'python_setup_py'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'ros'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['cmake', 'python']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'cmake'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'python'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['python_setup_py']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'python_setup_py'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'ros'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['cmake', 'python']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'cmake'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'python'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['python_setup_py']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'python'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['python_setup_py']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'ros'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['cmake', 'python']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['python_setup_py']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'python_setup_py'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['cmake', 'python']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'cmake'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'python'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['python_setup_py']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'python_setup_py'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['ignore', 'ignore_ament_install']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'ignore'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'ignore_ament_install'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['colcon_pkg']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'colcon_pkg'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['colcon_meta']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'colcon_meta'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['ros']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'ros'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['cmake', 'python']
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'cmake'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'python'
[0.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['python_setup_py']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'colcon_meta'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['ros']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'ros'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['cmake', 'python']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'cmake'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'python'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['python_setup_py']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'python_setup_py'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'ignore_ament_install'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['colcon_pkg']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'colcon_pkg'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['colcon_meta']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'ros'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['cmake', 'python']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'cmake'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'python'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['python_setup_py']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'python_setup_py'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'ros'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['cmake', 'python']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'cmake'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'python'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['python_setup_py']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'python_setup_py'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'ros'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['cmake', 'python']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'cmake'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'python'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['python_setup_py']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'python_setup_py'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'ros'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['cmake', 'python']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'cmake'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'python'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['python_setup_py']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'python_setup_py'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'python'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['python_setup_py']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'python_setup_py'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'ignore_ament_install'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'python'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['python_setup_py']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'ros'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['cmake', 'python']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'cmake'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'python'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['python_setup_py']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'ros'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['cmake', 'python']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'cmake'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'python'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['python_setup_py']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'python_setup_py'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'ros'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['cmake', 'python']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'cmake'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'python'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['python_setup_py']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'python_setup_py'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'python'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['python_setup_py']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'python_setup_py'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'python'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['python_setup_py']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'python_setup_py'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'ros'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['cmake', 'python']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'cmake'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'python'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['python_setup_py']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'python_setup_py'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'ros'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['cmake', 'python']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'cmake'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'python'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'ros'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['cmake', 'python']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'cmake'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'python'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'ros'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['cmake', 'python']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'cmake'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'python'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['python_setup_py']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'python_setup_py'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'ros'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['cmake', 'python']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'cmake'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'python'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['python_setup_py']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'python_setup_py'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'ros'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['cmake', 'python']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'cmake'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'python'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['python_setup_py']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'python_setup_py'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'ros'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['cmake', 'python']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'cmake'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'python'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['python_setup_py']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'python_setup_py'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'ros'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['cmake', 'python']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'cmake'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'python'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['python_setup_py']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'python_setup_py'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'ros'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['cmake', 'python']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'cmake'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'python'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['python_setup_py']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'python_setup_py'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'ros'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['cmake', 'python']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'cmake'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'python'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['python_setup_py']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'python_setup_py'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'ros'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['cmake', 'python']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'cmake'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'python'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['python_setup_py']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'python_setup_py'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['colcon_pkg']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'ros'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['cmake', 'python']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'cmake'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'python'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['python_setup_py']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'python_setup_py'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'ignore'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'ignore_ament_install'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['ros']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'ros'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['cmake', 'python']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'cmake'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'python'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['python_setup_py']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'python_setup_py'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'ignore'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'ignore_ament_install'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['colcon_pkg']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'colcon_pkg'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['colcon_meta']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'colcon_meta'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['cmake', 'python']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'cmake'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'python'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['python_setup_py']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'python_setup_py'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['ignore', 'ignore_ament_install']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'ignore'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'ignore_ament_install'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['colcon_pkg']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'colcon_pkg'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['colcon_meta']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'colcon_meta'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['cmake', 'python']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'cmake'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'python'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['python_setup_py']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'python_setup_py'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extensions ['ignore', 'ignore_ament_install']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'ignore'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'ignore_ament_install'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extensions ['colcon_pkg']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'colcon_pkg'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extensions ['colcon_meta']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'colcon_meta'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extensions ['ros']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'ros'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extensions ['cmake', 'python']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'cmake'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'python'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extensions ['python_setup_py']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201709) by extension 'python_setup_py'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['ignore', 'ignore_ament_install']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ignore'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ignore_ament_install'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['colcon_pkg']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'colcon_pkg'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['colcon_meta']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'colcon_meta'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['ros']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ros'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['cmake', 'python']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'cmake'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'python'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['python_setup_py']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'python_setup_py'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['ignore', 'ignore_ament_install']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ignore'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ignore_ament_install'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['colcon_pkg']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'colcon_pkg'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['colcon_meta']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'colcon_meta'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['ros']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ros'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['cmake', 'python']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'cmake'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'python'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['python_setup_py']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'python_setup_py'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['ignore', 'ignore_ament_install']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ignore'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ignore_ament_install'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['colcon_pkg']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'colcon_pkg'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['colcon_meta']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'colcon_meta'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['ros']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ros'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['cmake', 'python']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'cmake'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'python'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['python_setup_py']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'python_setup_py'
[0.243s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.243s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.243s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.243s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.243s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.261s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.262s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/haique_work/usv_control/project2/USVControl-user/install
[0.263s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 273 installed packages in /opt/ros/humble
[0.264s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.295s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.295s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.295s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.295s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.295s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.296s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.296s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.296s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.296s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.296s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.296s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.297s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.297s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.298s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning'
[0.301s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.301s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.301s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.312s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[2.100s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[2.109s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[2.131s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[2.132s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[2.134s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[2.134s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[2.135s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[2.135s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[2.136s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[2.136s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[2.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[2.138s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[2.138s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[2.138s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[2.139s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[2.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[2.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[2.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.140s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[2.141s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[2.141s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[2.142s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[2.143s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[2.143s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[2.144s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[2.144s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[2.145s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[2.145s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[2.145s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[2.146s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[2.146s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[2.147s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[2.147s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[2.147s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[2.147s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[2.148s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[2.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[2.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[2.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.149s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[2.150s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[2.150s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[2.151s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[2.151s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[2.152s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[2.152s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.153s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.153s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[2.153s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.158s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.158s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.159s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.168s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.168s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.ps1'
[2.169s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_ps1.py'
[2.170s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.ps1'
[2.171s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.sh'
[2.172s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_sh.py'
[2.173s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.sh'
[2.174s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.bash'
[2.174s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.bash'
[2.175s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.zsh'
[2.176s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.zsh'
