[0.144s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.144s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x714d5341c730>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x714d5341c1c0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x714d5341c1c0>>)
[0.210s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.211s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.211s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/haique_work/usv_control/project2/USVControl-user'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.222s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.222s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.223s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.224s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore_ament_install'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_pkg']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_pkg'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_meta']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_meta'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ros']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ros'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['cmake', 'python']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'cmake'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['python_setup_py']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python_setup_py'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ignore', 'ignore_ament_install']
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore'
[0.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ros'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['cmake', 'python']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'cmake'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['python_setup_py']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python_setup_py'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ignore', 'ignore_ament_install']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore_ament_install'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_pkg']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_pkg'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_meta']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_meta'
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ros']
[0.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['python_setup_py']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python_setup_py'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ignore', 'ignore_ament_install']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore_ament_install'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_pkg']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_pkg'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_meta']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_meta'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ros']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ros'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['cmake', 'python']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'cmake'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python'
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['python_setup_py']
[0.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ignore', 'ignore_ament_install']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore_ament_install'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_pkg']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_pkg'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_meta']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_meta'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ros']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ros'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['cmake', 'python']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'cmake'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['python_setup_py']
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python_setup_py'
[0.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_pkg'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_meta']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_meta'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ros']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ros'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['cmake', 'python']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'cmake'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['python_setup_py']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python_setup_py'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ignore', 'ignore_ament_install']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore_ament_install'
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_pkg']
[0.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['python_setup_py']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python_setup_py'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ignore', 'ignore_ament_install']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore_ament_install'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_pkg']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_pkg'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_meta']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_meta'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ros']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ros'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['cmake', 'python']
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'cmake'
[0.230s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['python_setup_py']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python_setup_py'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ignore', 'ignore_ament_install']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore_ament_install'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_pkg']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_pkg'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_meta']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_meta'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ros']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ros'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['cmake', 'python']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'cmake'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python'
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['python_setup_py']
[0.231s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'ignore_ament_install'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['colcon_pkg']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'colcon_pkg'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['colcon_meta']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'colcon_meta'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['ros']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'ros'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['cmake', 'python']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'cmake'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'python'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extensions ['python_setup_py']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154008) by extension 'python_setup_py'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['ignore', 'ignore_ament_install']
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'ignore'
[0.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'ros'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['cmake', 'python']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'cmake'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'python'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extensions ['python_setup_py']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154325) by extension 'python_setup_py'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['ignore', 'ignore_ament_install']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'ignore'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'ignore_ament_install'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['colcon_pkg']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'colcon_pkg'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['colcon_meta']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'colcon_meta'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['ros']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'ros'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['cmake', 'python']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'cmake'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'python'
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extensions ['python_setup_py']
[0.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154525) by extension 'python_setup_py'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'ros'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['cmake', 'python']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'cmake'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'python'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extensions ['python_setup_py']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154657) by extension 'python_setup_py'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['ignore', 'ignore_ament_install']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'ignore'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'ignore_ament_install'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['colcon_pkg']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'colcon_pkg'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['colcon_meta']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'colcon_meta'
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['ros']
[0.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'ros'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['cmake', 'python']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'cmake'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'python'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extensions ['python_setup_py']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_154839) by extension 'python_setup_py'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['colcon_meta']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'colcon_meta'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['ros']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'ros'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['cmake', 'python']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'cmake'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'python'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extensions ['python_setup_py']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155236) by extension 'python_setup_py'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['ignore', 'ignore_ament_install']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'ignore'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'ignore_ament_install'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['colcon_pkg']
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'colcon_pkg'
[0.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'ros'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['cmake', 'python']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'cmake'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'python'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extensions ['python_setup_py']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155243) by extension 'python_setup_py'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['ignore', 'ignore_ament_install']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'ignore'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'ignore_ament_install'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['colcon_pkg']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'colcon_pkg'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['colcon_meta']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'colcon_meta'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['ros']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'ros'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['cmake', 'python']
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'cmake'
[0.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'python'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extensions ['python_setup_py']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155454) by extension 'python_setup_py'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'colcon_meta'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['ros']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'ros'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['cmake', 'python']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'cmake'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'python'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extensions ['python_setup_py']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_155815) by extension 'python_setup_py'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['ignore', 'ignore_ament_install']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'ignore'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'ignore_ament_install'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['colcon_pkg']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'colcon_pkg'
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['colcon_meta']
[0.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'ros'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['cmake', 'python']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'cmake'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'python'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extensions ['python_setup_py']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160101) by extension 'python_setup_py'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'ignore_ament_install'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['colcon_pkg']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'colcon_pkg'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['colcon_meta']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'colcon_meta'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['ros']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'ros'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['cmake', 'python']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'cmake'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'python'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extensions ['python_setup_py']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160239) by extension 'python_setup_py'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['ignore', 'ignore_ament_install']
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'ignore'
[0.238s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'ignore_ament_install'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['ros']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'ros'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['cmake', 'python']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'cmake'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'python'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extensions ['python_setup_py']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160505) by extension 'python_setup_py'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['ignore', 'ignore_ament_install']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'ignore'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'ignore_ament_install'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['colcon_pkg']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'colcon_pkg'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['colcon_meta']
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'colcon_meta'
[0.239s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['cmake', 'python']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'cmake'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'python'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extensions ['python_setup_py']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_160828) by extension 'python_setup_py'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['ignore', 'ignore_ament_install']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'ignore'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'ignore_ament_install'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['colcon_pkg']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'colcon_pkg'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['colcon_meta']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'colcon_meta'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['ros']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'ros'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['cmake', 'python']
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'cmake'
[0.240s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'python'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extensions ['python_setup_py']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162631) by extension 'python_setup_py'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['ignore', 'ignore_ament_install']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'ignore'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'ignore_ament_install'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['colcon_pkg']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'colcon_pkg'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['colcon_meta']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'colcon_meta'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['ros']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'ros'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['cmake', 'python']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'cmake'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'python'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extensions ['python_setup_py']
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_162831) by extension 'python_setup_py'
[0.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['ignore', 'ignore_ament_install']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'ignore'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'ignore_ament_install'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['colcon_pkg']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'colcon_pkg'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['colcon_meta']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'colcon_meta'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['ros']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'ros'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['cmake', 'python']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'cmake'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'python'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extensions ['python_setup_py']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163455) by extension 'python_setup_py'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['ignore', 'ignore_ament_install']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'ignore'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'ignore_ament_install'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['colcon_pkg']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'colcon_pkg'
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['colcon_meta']
[0.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'colcon_meta'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['ros']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'ros'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['cmake', 'python']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'cmake'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'python'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extensions ['python_setup_py']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163759) by extension 'python_setup_py'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['ignore', 'ignore_ament_install']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'ignore'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'ignore_ament_install'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['colcon_pkg']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'colcon_pkg'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['colcon_meta']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'colcon_meta'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['ros']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'ros'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['cmake', 'python']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'cmake'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'python'
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extensions ['python_setup_py']
[0.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_163930) by extension 'python_setup_py'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['ignore', 'ignore_ament_install']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'ignore'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'ignore_ament_install'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['colcon_pkg']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'colcon_pkg'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['colcon_meta']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'colcon_meta'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['ros']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'ros'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['cmake', 'python']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'cmake'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'python'
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extensions ['python_setup_py']
[0.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164043) by extension 'python_setup_py'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['ignore', 'ignore_ament_install']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'ignore'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'ignore_ament_install'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['colcon_pkg']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'colcon_pkg'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['colcon_meta']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'colcon_meta'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['ros']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'ros'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['cmake', 'python']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'cmake'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'python'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extensions ['python_setup_py']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164254) by extension 'python_setup_py'
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['ignore', 'ignore_ament_install']
[0.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'ignore_ament_install'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['colcon_pkg']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'colcon_pkg'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['colcon_meta']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'colcon_meta'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['ros']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'ros'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['cmake', 'python']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'cmake'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'python'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extensions ['python_setup_py']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164431) by extension 'python_setup_py'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['ignore', 'ignore_ament_install']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'ignore'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'ignore_ament_install'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['colcon_pkg']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'colcon_pkg'
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['colcon_meta']
[0.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'ros'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['cmake', 'python']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'cmake'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'python'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extensions ['python_setup_py']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164547) by extension 'python_setup_py'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['ignore', 'ignore_ament_install']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'ignore'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['colcon_meta']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'ros'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['cmake', 'python']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'cmake'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'python'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extensions ['python_setup_py']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164707) by extension 'python_setup_py'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['ignore', 'ignore_ament_install']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'ignore'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'ignore_ament_install'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['colcon_pkg']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'colcon_pkg'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['colcon_meta']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'colcon_meta'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['ros']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'ros'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['cmake', 'python']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'cmake'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'python'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extensions ['python_setup_py']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_164914) by extension 'python_setup_py'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['ignore', 'ignore_ament_install']
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'ignore'
[0.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'ignore_ament_install'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['colcon_pkg']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'colcon_pkg'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['colcon_meta']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'colcon_meta'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['ros']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'ros'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['cmake', 'python']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'cmake'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'python'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extensions ['python_setup_py']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165304) by extension 'python_setup_py'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['ignore', 'ignore_ament_install']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'ignore'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'ignore_ament_install'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['colcon_pkg']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'colcon_pkg'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['colcon_meta']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'colcon_meta'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['ros']
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'ros'
[0.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['cmake', 'python']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'cmake'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'python'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extensions ['python_setup_py']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165441) by extension 'python_setup_py'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['ignore', 'ignore_ament_install']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'ignore'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'ignore_ament_install'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['colcon_pkg']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'colcon_pkg'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['colcon_meta']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'colcon_meta'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['ros']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'ros'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['cmake', 'python']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'cmake'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'python'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extensions ['python_setup_py']
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165637) by extension 'python_setup_py'
[0.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['ignore', 'ignore_ament_install']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'ignore'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'ignore_ament_install'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['colcon_pkg']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'colcon_pkg'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['colcon_meta']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'colcon_meta'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['ros']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'ros'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['cmake', 'python']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'cmake'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'python'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extensions ['python_setup_py']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_165936) by extension 'python_setup_py'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['ignore', 'ignore_ament_install']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'ignore'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'ignore_ament_install'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['colcon_pkg']
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'colcon_pkg'
[0.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['colcon_meta']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'colcon_meta'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['ros']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'ros'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['cmake', 'python']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'cmake'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'python'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extensions ['python_setup_py']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170125) by extension 'python_setup_py'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['ignore', 'ignore_ament_install']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'ignore'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'ignore_ament_install'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['colcon_pkg']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'colcon_pkg'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['colcon_meta']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'colcon_meta'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['ros']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'ros'
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['cmake', 'python']
[0.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'cmake'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'python'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extensions ['python_setup_py']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170251) by extension 'python_setup_py'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['ignore', 'ignore_ament_install']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'ignore'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'ignore_ament_install'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['colcon_pkg']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'colcon_pkg'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['colcon_meta']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'colcon_meta'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['ros']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'ros'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['cmake', 'python']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'cmake'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'python'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extensions ['python_setup_py']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170405) by extension 'python_setup_py'
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['ignore', 'ignore_ament_install']
[0.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'ignore'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'ignore_ament_install'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['colcon_pkg']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'colcon_pkg'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['colcon_meta']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'colcon_meta'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['ros']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'ros'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['cmake', 'python']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'cmake'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'python'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extensions ['python_setup_py']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170607) by extension 'python_setup_py'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['ignore', 'ignore_ament_install']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'ignore'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'ignore_ament_install'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['colcon_pkg']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'colcon_pkg'
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['colcon_meta']
[0.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'colcon_meta'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['ros']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'ros'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['cmake', 'python']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'cmake'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'python'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extensions ['python_setup_py']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170612) by extension 'python_setup_py'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['ignore', 'ignore_ament_install']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'ignore'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'ignore_ament_install'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['colcon_pkg']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'colcon_pkg'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['colcon_meta']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'colcon_meta'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['ros']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'ros'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['cmake', 'python']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'cmake'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'python'
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extensions ['python_setup_py']
[0.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_170741) by extension 'python_setup_py'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['ignore', 'ignore_ament_install']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'ignore'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'ignore_ament_install'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['colcon_pkg']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'colcon_pkg'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['colcon_meta']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'colcon_meta'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['ros']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'ros'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['cmake', 'python']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'cmake'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'python'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extensions ['python_setup_py']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171024) by extension 'python_setup_py'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['ignore', 'ignore_ament_install']
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'ignore'
[0.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'ignore_ament_install'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['colcon_pkg']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'colcon_pkg'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['colcon_meta']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'colcon_meta'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['ros']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'ros'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['cmake', 'python']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'cmake'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'python'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extensions ['python_setup_py']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171230) by extension 'python_setup_py'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'ignore'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'ignore_ament_install'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['colcon_pkg']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'colcon_pkg'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['colcon_meta']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'colcon_meta'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['ros']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'ros'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['cmake', 'python']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'cmake'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'python'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extensions ['python_setup_py']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171416) by extension 'python_setup_py'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'ignore_ament_install'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['colcon_pkg']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'colcon_pkg'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['colcon_meta']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'colcon_meta'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['ros']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'ros'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['cmake', 'python']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'cmake'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'python'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extensions ['python_setup_py']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171536) by extension 'python_setup_py'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['ignore', 'ignore_ament_install']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'ignore'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'ignore_ament_install'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['colcon_pkg']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'colcon_pkg'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['colcon_meta']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'colcon_meta'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['ros']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'ros'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['cmake', 'python']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'cmake'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'python'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extensions ['python_setup_py']
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171656) by extension 'python_setup_py'
[0.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['ignore', 'ignore_ament_install']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'ignore'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'ignore_ament_install'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['colcon_pkg']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'colcon_pkg'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['colcon_meta']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'colcon_meta'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['ros']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'ros'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['cmake', 'python']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'cmake'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'python'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extensions ['python_setup_py']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_171909) by extension 'python_setup_py'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['ignore', 'ignore_ament_install']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'ignore'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'ignore_ament_install'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['colcon_pkg']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'colcon_pkg'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['colcon_meta']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'colcon_meta'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['ros']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'ros'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['cmake', 'python']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'cmake'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'python'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extensions ['python_setup_py']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172219) by extension 'python_setup_py'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['ignore', 'ignore_ament_install']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'ignore'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'ignore_ament_install'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['colcon_pkg']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'colcon_pkg'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['colcon_meta']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'colcon_meta'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['ros']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'ros'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['cmake', 'python']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'cmake'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'python'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extensions ['python_setup_py']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172241) by extension 'python_setup_py'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['ignore', 'ignore_ament_install']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'ignore'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'ignore_ament_install'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['colcon_pkg']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'colcon_pkg'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['colcon_meta']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'colcon_meta'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['ros']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'ros'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['cmake', 'python']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'cmake'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'python'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extensions ['python_setup_py']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_172434) by extension 'python_setup_py'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['colcon_pkg']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'colcon_pkg'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['colcon_meta']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'colcon_meta'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['ros']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'ros'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['cmake', 'python']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'cmake'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'python'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extensions ['python_setup_py']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173004) by extension 'python_setup_py'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['ignore', 'ignore_ament_install']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['colcon_pkg']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'colcon_pkg'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['colcon_meta']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'colcon_meta'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['ros']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'ros'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['cmake', 'python']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'cmake'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'python'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extensions ['python_setup_py']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_173341) by extension 'python_setup_py'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'ignore'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'ignore_ament_install'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['colcon_pkg']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'colcon_pkg'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['colcon_meta']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'colcon_meta'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['ros']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'ros'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['cmake', 'python']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'cmake'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'python'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extensions ['python_setup_py']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_180715) by extension 'python_setup_py'
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['ignore', 'ignore_ament_install']
[0.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'ros'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['cmake', 'python']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'cmake'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'python'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extensions ['python_setup_py']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182520) by extension 'python_setup_py'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['ignore', 'ignore_ament_install']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'ignore'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'ignore_ament_install'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['colcon_pkg']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'colcon_pkg'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['colcon_meta']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'colcon_meta'
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['ros']
[0.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'ros'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['cmake', 'python']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'cmake'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'python'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extensions ['python_setup_py']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_182814) by extension 'python_setup_py'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['ignore', 'ignore_ament_install']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'ignore'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'ignore_ament_install'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['colcon_pkg']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'colcon_pkg'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['colcon_meta']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'colcon_meta'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['ros']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'ros'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['cmake', 'python']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'cmake'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'python'
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extensions ['python_setup_py']
[0.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183021) by extension 'python_setup_py'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['ignore', 'ignore_ament_install']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'ignore'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'ignore_ament_install'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['colcon_pkg']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'colcon_pkg'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['colcon_meta']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'colcon_meta'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['ros']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'ros'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['cmake', 'python']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'cmake'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'python'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extensions ['python_setup_py']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_183304) by extension 'python_setup_py'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['ignore', 'ignore_ament_install']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'ignore'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'ignore_ament_install'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['colcon_pkg']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'colcon_pkg'
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['colcon_meta']
[0.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'colcon_meta'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['ros']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'ros'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['cmake', 'python']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'cmake'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'python'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extensions ['python_setup_py']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184401) by extension 'python_setup_py'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['ignore', 'ignore_ament_install']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'ignore'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'ignore_ament_install'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['colcon_pkg']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'colcon_pkg'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['colcon_meta']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'colcon_meta'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['ros']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'ros'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['cmake', 'python']
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'cmake'
[0.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'python'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extensions ['python_setup_py']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184639) by extension 'python_setup_py'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['ignore', 'ignore_ament_install']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'ignore'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'ignore_ament_install'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['colcon_pkg']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'colcon_pkg'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['colcon_meta']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'colcon_meta'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['ros']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'ros'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['cmake', 'python']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'cmake'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'python'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extensions ['python_setup_py']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_184841) by extension 'python_setup_py'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['ignore', 'ignore_ament_install']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'ignore'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'ignore_ament_install'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['colcon_pkg']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'colcon_pkg'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['colcon_meta']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'colcon_meta'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['ros']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'ros'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['cmake', 'python']
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'cmake'
[0.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'python'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extensions ['python_setup_py']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185024) by extension 'python_setup_py'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['ignore', 'ignore_ament_install']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'ignore'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'ignore_ament_install'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['colcon_pkg']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'colcon_pkg'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['colcon_meta']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'colcon_meta'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['ros']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'ros'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['cmake', 'python']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'cmake'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'python'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extensions ['python_setup_py']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_185311) by extension 'python_setup_py'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['ignore', 'ignore_ament_install']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'ignore'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'ignore_ament_install'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['colcon_pkg']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'colcon_pkg'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['colcon_meta']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'colcon_meta'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['ros']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'ros'
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['cmake', 'python']
[0.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'cmake'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'python'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extensions ['python_setup_py']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191236) by extension 'python_setup_py'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['ignore', 'ignore_ament_install']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'ignore'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'ignore_ament_install'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['colcon_pkg']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'colcon_pkg'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['colcon_meta']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'colcon_meta'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['ros']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'ros'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['cmake', 'python']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'cmake'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'python'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extensions ['python_setup_py']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_191410) by extension 'python_setup_py'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['ignore', 'ignore_ament_install']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'ignore'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'ignore_ament_install'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['colcon_pkg']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'colcon_pkg'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['colcon_meta']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'colcon_meta'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['ros']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'ros'
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['cmake', 'python']
[0.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'cmake'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'python'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extensions ['python_setup_py']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200110) by extension 'python_setup_py'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'ignore_ament_install'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['colcon_pkg']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'colcon_pkg'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['colcon_meta']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'colcon_meta'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['ros']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'ros'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['cmake', 'python']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'cmake'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'python'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extensions ['python_setup_py']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200513) by extension 'python_setup_py'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['ignore', 'ignore_ament_install']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'ignore'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'ignore_ament_install'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['colcon_pkg']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'colcon_pkg'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['colcon_meta']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'colcon_meta'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['ros']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'ros'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['cmake', 'python']
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'cmake'
[0.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'python'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extensions ['python_setup_py']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_200637) by extension 'python_setup_py'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['ignore', 'ignore_ament_install']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'ignore'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'ignore_ament_install'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['colcon_pkg']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'colcon_pkg'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['colcon_meta']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'colcon_meta'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['ros']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'ros'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['cmake', 'python']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'cmake'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'python'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extensions ['python_setup_py']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201301) by extension 'python_setup_py'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['ignore', 'ignore_ament_install']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'ignore'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'ignore_ament_install'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['colcon_pkg']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'colcon_pkg'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['colcon_meta']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'colcon_meta'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['ros']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'ros'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['cmake', 'python']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'cmake'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'python'
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extensions ['python_setup_py']
[0.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_201550) by extension 'python_setup_py'
[0.273s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.274s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.297s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.297s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.299s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/haique_work/usv_control/project2/USVControl-user/install
[0.301s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 273 installed packages in /opt/ros/humble
[0.302s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.341s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.342s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.342s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.342s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.349s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.349s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.349s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning'
[0.351s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.351s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.351s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.366s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.571s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.585s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[0.619s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[0.619s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[0.622s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[0.622s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[0.623s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[0.623s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[0.624s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[0.625s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[0.626s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[0.626s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[0.626s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[0.627s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[0.628s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[0.628s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[0.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[0.629s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.630s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[0.630s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[0.631s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[0.632s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[0.633s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[0.633s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[0.634s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[0.634s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[0.635s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[0.635s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[0.635s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[0.636s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[0.636s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[0.637s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[0.637s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[0.637s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[0.638s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[0.638s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[0.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[0.639s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[0.640s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[0.640s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[0.641s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[0.641s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[0.642s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[0.642s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[0.643s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[0.644s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.645s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.645s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.645s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.650s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.651s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.651s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.663s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.664s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.ps1'
[0.665s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_ps1.py'
[0.667s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.ps1'
[0.668s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.sh'
[0.669s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_sh.py'
[0.675s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.sh'
[0.676s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.bash'
[0.676s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.bash'
[0.678s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.zsh'
[0.679s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.zsh'
