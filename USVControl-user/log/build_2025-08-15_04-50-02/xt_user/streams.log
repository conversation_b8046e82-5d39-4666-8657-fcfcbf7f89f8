[0.027s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[0.128s] [35m[1mConsolidate compiler generated dependencies of target planning_hpp[0m
[0.173s] [ 77%] Built target planning_hpp
[0.202s] [35m[1mConsolidate compiler generated dependencies of target xt_user_node[0m
[0.246s] [100%] Built target xt_user_node
[0.271s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[0.293s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
[0.307s] -- Install configuration: ""
[0.309s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[0.309s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[0.309s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[0.309s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[0.309s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[0.310s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[0.310s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[0.310s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[0.310s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[0.310s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[0.310s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.dsv
[0.311s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[0.311s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[0.311s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[0.311s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[0.313s] -- Up-to-date: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.xml
[0.326s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
