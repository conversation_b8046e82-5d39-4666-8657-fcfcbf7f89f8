[0.029s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[0.044s] [0mCMake Error: The current CMakeCache.txt directory /home/<USER>/333/project2_0815/USVControl-user/build/xt_user/CMakeCache.txt is different than the directory /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt[0m
[0.075s] [0mCMake Error: The source directory "/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning" does not exist.
[0.075s] Specify --help for usage, or press the help button on the CMake GUI.[0m
[0.079s] gmake: *** [Makefile:443：cmake_check_build_system] 错误 1
[0.091s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
