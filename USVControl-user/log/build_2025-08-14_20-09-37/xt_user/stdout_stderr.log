-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Override CMake install command with custom implementation using symlinks instead of copying resources
-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found OpenCV: /usr (found version "4.5.4") 
-- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") 
-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system 
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/include/boat_datadef.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::CalculateTrackAccuracy()[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:242:15:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpaired_actual_points[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  242 |         float [01;35m[Kpaired_actual_points[m[K[10][2] = {0}; // 用于存储配对好的实际点，方便打印
      |               [01;35m[K^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Knowpos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  323 |         double [01;35m[Knowpos[m[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
      |                [01;35m[K^~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:33:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Khomepos[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  323 |         double nowpos[2] = {0}, [01;35m[Khomepos[m[K[2] = {0}, lengthangle[2] = {0};
      |                                 [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:51:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  323 |         double nowpos[2] = {0}, homepos[2] = {0}, [01;35m[Klengthangle[m[K[2] = {0};
      |                                                   [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:448:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  448 |         float [01;35m[Klengthangle[m[K[2] = {0}, vel = 0;    // 长度角度和速度变量
      |               [01;35m[K^~~~~~~~~~~[m[K
[ 77%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[ 77%] Built target planning_hpp
[ 88%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:182:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  182 |     kinematic_state [01;35m[Kpreposture[m[K;
      |                     [01;35m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  212 |     double [01;35m[Kstime[m[K, etime;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  212 |     double stime, [01;35m[Ketime[m[K;
      |                   [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:256:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  256 |     double [01;35m[Kstart[m[K,end;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:256:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  256 |     double start,[01;35m[Kend[m[K;
      |                  [01;35m[K^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:257:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  257 |     int [01;35m[Kdatanum[m[K = 0;
      |         [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:259:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  259 |     unsigned char [01;35m[Kperiod[m[K = 0;
      |                   [01;35m[K^~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  254 | void* MapShow([01;35m[Kvoid *arg[m[K)
      |               [01;35m[K~~~~~~^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  309 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  310 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  311 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  312 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  313 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:314:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  314 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:315:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  315 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:316:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  316 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:317:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  317 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:318:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
  318 |             [01;35m[K}[m[K;
      |             [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  331 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
      |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:411:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
  411 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
      |                                                            [01;35m[K^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:449:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  449 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetError(),       // 当前误差 (ERR)
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:450:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  450 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetErrorChange(), // 误差变化 (EC)
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:451:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  451 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetSumErr(),      // 积分累积 (SUM_ERR)
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:452:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  452 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetKp(),          // 最终Kp
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:454:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  454 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetDeltaKi(),     // Ki模糊调整量
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:455:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  455 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetKd(),          // 最终Kd
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:457:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  457 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetDeltaKd(),     // Kd模糊调整量
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:459:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  459 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetP_Out(),       // 比例项输出
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:460:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  460 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetI_Out(),       // 积分项输出
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:461:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
  461 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetD_Out()        // 微分项输出
      |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
   30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
      |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:335:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  335 |     double start, [01;35m[Kend[m[K;
      |                   [01;35m[K^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:336:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  336 |     double [01;35m[Kduration[m[K[10];
      |            [01;35m[K^~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:337:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  337 |     int [01;35m[Kdatanum[m[K = 0;
      |         [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:390:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  390 |     double [01;35m[Kanglevel[m[K[2];
      |            [01;35m[K^~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/xt_user_node.dir/build.make:76：CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:165：CMakeFiles/xt_user_node.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
