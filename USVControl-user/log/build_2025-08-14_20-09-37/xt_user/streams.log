[0.014s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user
[0.069s] -- The C compiler identification is GNU 11.4.0
[0.131s] -- The CXX compiler identification is GNU 11.4.0
[0.141s] -- Detecting C compiler ABI info
[0.212s] -- Detecting C compiler ABI info - done
[0.219s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.219s] -- Detecting C compile features
[0.220s] -- Detecting C compile features - done
[0.222s] -- Detecting CXX compiler ABI info
[0.305s] -- Detecting CXX compiler AB<PERSON> info - done
[0.312s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.312s] -- Detecting CXX compile features
[0.313s] -- Detecting CXX compile features - done
[0.314s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.667s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.725s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.742s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.778s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.783s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.790s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.802s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.816s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.854s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.856s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.954s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.980s] -- Found FastRTPS: /opt/ros/humble/include  
[1.015s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.022s] -- Looking for pthread.h
[1.102s] -- Looking for pthread.h - found
[1.103s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.184s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.186s] -- Found Threads: TRUE  
[1.236s] -- Found OpenCV: /usr (found version "4.5.4") 
[1.238s] -- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") 
[1.239s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[1.271s] -- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system 
[1.332s] -- Configuring done
[1.353s] -- Generating done
[1.357s] -- Build files have been written to: /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[1.371s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning -DAMENT_CMAKE_SYMLINK_INSTALL=1 -DCMAKE_INSTALL_PREFIX=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user
[1.371s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[1.426s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/include/boat_datadef.cpp.o[0m
[1.426s] [ 22%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[1.427s] [ 33%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o[0m
[1.427s] [ 44%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o[0m
[1.427s] [ 55%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o[0m
[1.427s] [ 66%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[1.768s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::CalculateTrackAccuracy()[m[K’:
[1.769s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:242:15:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpaired_actual_points[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[1.769s]   242 |         float [01;35m[Kpaired_actual_points[m[K[10][2] = {0}; // 用于存储配对好的实际点，方便打印
[1.769s]       |               [01;35m[K^~~~~~~~~~~~~~~~~~~~[m[K
[1.769s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[1.769s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Knowpos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[1.769s]   323 |         double [01;35m[Knowpos[m[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
[1.769s]       |                [01;35m[K^~~~~~[m[K
[1.769s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:33:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Khomepos[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[1.769s]   323 |         double nowpos[2] = {0}, [01;35m[Khomepos[m[K[2] = {0}, lengthangle[2] = {0};
[1.769s]       |                                 [01;35m[K^~~~~~~[m[K
[1.770s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:51:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[1.770s]   323 |         double nowpos[2] = {0}, homepos[2] = {0}, [01;35m[Klengthangle[m[K[2] = {0};
[1.770s]       |                                                   [01;35m[K^~~~~~~~~~~[m[K
[1.770s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[1.770s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:448:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[1.770s]   448 |         float [01;35m[Klengthangle[m[K[2] = {0}, vel = 0;    // 长度角度和速度变量
[1.770s]       |               [01;35m[K^~~~~~~~~~~[m[K
[2.083s] [ 77%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[2.205s] [ 77%] Built target planning_hpp
[2.228s] [ 88%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[5.570s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[5.570s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:182:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.571s]   182 |     kinematic_state [01;35m[Kpreposture[m[K;
[5.571s]       |                     [01;35m[K^~~~~~~~~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.575s]   212 |     double [01;35m[Kstime[m[K, etime;
[5.575s]       |            [01;35m[K^~~~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.575s]   212 |     double stime, [01;35m[Ketime[m[K;
[5.575s]       |                   [01;35m[K^~~~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:256:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.575s]   256 |     double [01;35m[Kstart[m[K,end;
[5.575s]       |            [01;35m[K^~~~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:256:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.575s]   256 |     double start,[01;35m[Kend[m[K;
[5.575s]       |                  [01;35m[K^~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:257:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.575s]   257 |     int [01;35m[Kdatanum[m[K = 0;
[5.575s]       |         [01;35m[K^~~~~~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:259:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.575s]   259 |     unsigned char [01;35m[Kperiod[m[K = 0;
[5.575s]       |                   [01;35m[K^~~~~~[m[K
[5.575s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[5.575s]   254 | void* MapShow([01;35m[Kvoid *arg[m[K)
[5.575s]       |               [01;35m[K~~~~~~^~~[m[K
[5.590s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[5.590s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.590s]   309 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[5.590s]       |                 [01;35m[K^[m[K
[5.595s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.595s]   310 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[5.595s]       |                 [01;35m[K^[m[K
[5.603s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.603s]   311 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[5.603s]       |                 [01;35m[K^[m[K
[5.607s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.607s]   312 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[5.607s]       |                 [01;35m[K^[m[K
[5.607s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.608s]   313 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[5.608s]       |                 [01;35m[K^[m[K
[5.608s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:314:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.608s]   314 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[5.608s]       |                 [01;35m[K^[m[K
[5.608s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:315:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.608s]   315 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[5.608s]       |                 [01;35m[K^[m[K
[5.608s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:316:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.608s]   316 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[5.609s]       |                 [01;35m[K^[m[K
[5.609s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:317:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.609s]   317 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[5.609s]       |                 [01;35m[K^[m[K
[5.609s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:318:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[5.609s]   318 |             [01;35m[K}[m[K;
[5.609s]       |             [01;35m[K^[m[K
[5.610s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.610s]   331 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[5.610s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[5.696s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:411:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[5.696s]   411 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[5.696s]       |                                                            [01;35m[K^~~[m[K
[5.697s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:449:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.697s]   449 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetError(),       // 当前误差 (ERR)
[5.697s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.697s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.697s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.697s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.698s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.698s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:450:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.698s]   450 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetErrorChange(), // 误差变化 (EC)
[5.698s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.698s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.698s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.698s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.698s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.698s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:451:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.698s]   451 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetSumErr(),      // 积分累积 (SUM_ERR)
[5.698s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.698s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.698s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.698s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.699s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.699s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:452:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.699s]   452 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetKp(),          // 最终Kp
[5.699s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.699s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.699s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.699s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.699s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.699s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:454:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.699s]   454 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetDeltaKi(),     // Ki模糊调整量
[5.699s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.699s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.699s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.699s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.699s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.700s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:455:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.700s]   455 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetKd(),          // 最终Kd
[5.700s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.700s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.700s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.700s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.700s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.700s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:457:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.700s]   457 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetDeltaKd(),     // Kd模糊调整量
[5.700s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.700s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.700s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.700s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.700s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.700s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:459:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.700s]   459 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetP_Out(),       // 比例项输出
[5.701s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.701s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.701s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.701s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.701s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.701s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:460:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.701s]   460 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetI_Out(),       // 积分项输出
[5.701s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.701s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.701s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.701s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.701s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.701s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:461:32:[m[K [01;31m[Kerror: [m[K‘[01m[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller[m[K’ is private within this context
[5.701s]   461 |                     controller.[01;31m[Kfuzzyheadingcontroller[m[K.GetD_Out()        // 微分项输出
[5.701s]       |                                [01;31m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.701s] In file included from [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42[m[K:
[5.701s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:[m[K [01;36m[Knote: [m[Kdeclared private here
[5.701s]    30 |         FuzzyPIDControl [01;36m[Kfuzzyheadingcontroller[m[K;
[5.702s]       |                         [01;36m[K^~~~~~~~~~~~~~~~~~~~~~[m[K
[5.703s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:335:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.703s]   335 |     double start, [01;35m[Kend[m[K;
[5.703s]       |                   [01;35m[K^~~[m[K
[5.703s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:336:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.703s]   336 |     double [01;35m[Kduration[m[K[10];
[5.703s]       |            [01;35m[K^~~~~~~~[m[K
[5.703s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:337:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.703s]   337 |     int [01;35m[Kdatanum[m[K = 0;
[5.703s]       |         [01;35m[K^~~~~~~[m[K
[5.703s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:390:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.703s]   390 |     double [01;35m[Kanglevel[m[K[2];
[5.703s]       |            [01;35m[K^~~~~~~~[m[K
[9.529s] gmake[2]: *** [CMakeFiles/xt_user_node.dir/build.make:76：CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o] 错误 1
[9.530s] gmake[1]: *** [CMakeFiles/Makefile2:165：CMakeFiles/xt_user_node.dir/all] 错误 2
[9.530s] gmake: *** [Makefile:146：all] 错误 2
[9.533s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
