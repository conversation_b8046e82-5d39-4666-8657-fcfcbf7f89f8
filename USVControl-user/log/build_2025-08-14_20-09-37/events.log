[0.000000] (-) TimerEvent: {}
[0.000172] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.000214] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.009890] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.010671] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning', '-DAMENT_CMAKE_SYMLINK_INSTALL=1', '-<PERSON><PERSON><PERSON>_INSTALL_PREFIX=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user'], 'cwd': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'haique'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1916'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haique'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haique'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/haique:@/tmp/.ICE-unix/1916,unix/haique:/tmp/.ICE-unix/1916'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ee699f72_6152_4c72_b626_1c2c2fa14a12'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.F70RA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble')]), 'shell': False}
[0.069598] (xt_user) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.099175] (-) TimerEvent: {}
[0.130800] (xt_user) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.140653] (xt_user) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.199330] (-) TimerEvent: {}
[0.212359] (xt_user) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.219266] (xt_user) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.219533] (xt_user) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.219848] (xt_user) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.222073] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.299442] (-) TimerEvent: {}
[0.305378] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.312251] (xt_user) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.312502] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.312963] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.314650] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.399577] (-) TimerEvent: {}
[0.500116] (-) TimerEvent: {}
[0.600425] (-) TimerEvent: {}
[0.667261] (xt_user) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.700576] (-) TimerEvent: {}
[0.725011] (xt_user) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.742252] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.778180] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.783061] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.789914] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.800923] (-) TimerEvent: {}
[0.801978] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.816381] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.854160] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.855790] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.901039] (-) TimerEvent: {}
[0.954332] (xt_user) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.979987] (xt_user) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[1.002177] (-) TimerEvent: {}
[1.015329] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.022524] (xt_user) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.101710] (xt_user) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.102807] (xt_user) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.102958] (-) TimerEvent: {}
[1.183936] (xt_user) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.185858] (xt_user) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.203139] (-) TimerEvent: {}
[1.235967] (xt_user) StdoutLine: {'line': b'-- Found OpenCV: /usr (found version "4.5.4") \n'}
[1.238136] (xt_user) StdoutLine: {'line': b'-- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") \n'}
[1.239033] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[1.271078] (xt_user) StdoutLine: {'line': b'-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system \n'}
[1.303289] (-) TimerEvent: {}
[1.332148] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[1.353346] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[1.356843] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user\n'}
[1.369655] (xt_user) CommandEnded: {'returncode': 0}
[1.371326] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[1.371376] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'haique'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1916'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haique'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haique'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/haique:@/tmp/.ICE-unix/1916,unix/haique:/tmp/.ICE-unix/1916'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ee699f72_6152_4c72_b626_1c2c2fa14a12'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.F70RA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble')]), 'shell': False}
[1.403523] (-) TimerEvent: {}
[1.426333] (xt_user) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/include/boat_datadef.cpp.o\x1b[0m\n'}
[1.426540] (xt_user) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o\x1b[0m\n'}
[1.426652] (xt_user) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o\x1b[0m\n'}
[1.426842] (xt_user) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o\x1b[0m\n'}
[1.426928] (xt_user) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o\x1b[0m\n'}
[1.427127] (xt_user) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[1.504087] (-) TimerEvent: {}
[1.604400] (-) TimerEvent: {}
[1.707818] (-) TimerEvent: {}
[1.768338] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::CalculateTrackAccuracy()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.768830] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:242:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kpaired_actual_points\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.769000] (xt_user) StderrLine: {'line': b'  242 |         float \x1b[01;35m\x1b[Kpaired_actual_points\x1b[m\x1b[K[10][2] = {0}; // \xe7\x94\xa8\xe4\xba\x8e\xe5\xad\x98\xe5\x82\xa8\xe9\x85\x8d\xe5\xaf\xb9\xe5\xa5\xbd\xe7\x9a\x84\xe5\xae\x9e\xe9\x99\x85\xe7\x82\xb9\xef\xbc\x8c\xe6\x96\xb9\xe4\xbe\xbf\xe6\x89\x93\xe5\x8d\xb0\n'}
[1.769087] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.769168] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.769250] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Knowpos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.769345] (xt_user) StderrLine: {'line': b'  323 |         double \x1b[01;35m\x1b[Knowpos\x1b[m\x1b[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};\n'}
[1.769424] (xt_user) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[1.769500] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:33:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Khomepos\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.769577] (xt_user) StderrLine: {'line': b'  323 |         double nowpos[2] = {0}, \x1b[01;35m\x1b[Khomepos\x1b[m\x1b[K[2] = {0}, lengthangle[2] = {0};\n'}
[1.769655] (xt_user) StderrLine: {'line': b'      |                                 \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[1.769745] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:323:51:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Klengthangle\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.769846] (xt_user) StderrLine: {'line': b'  323 |         double nowpos[2] = {0}, homepos[2] = {0}, \x1b[01;35m\x1b[Klengthangle\x1b[m\x1b[K[2] = {0};\n'}
[1.770275] (xt_user) StderrLine: {'line': b'      |                                                   \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.770369] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.770449] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:448:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Klengthangle\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.770535] (xt_user) StderrLine: {'line': b'  448 |         float \x1b[01;35m\x1b[Klengthangle\x1b[m\x1b[K[2] = {0}, vel = 0;    // \xe9\x95\xbf\xe5\xba\xa6\xe8\xa7\x92\xe5\xba\xa6\xe5\x92\x8c\xe9\x80\x9f\xe5\xba\xa6\xe5\x8f\x98\xe9\x87\x8f\n'}
[1.770621] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.807961] (-) TimerEvent: {}
[1.908890] (-) TimerEvent: {}
[2.009990] (-) TimerEvent: {}
[2.083435] (xt_user) StdoutLine: {'line': b'[ 77%] \x1b[32m\x1b[1mLinking CXX shared library libplanning_hpp.so\x1b[0m\n'}
[2.110115] (-) TimerEvent: {}
[2.204636] (xt_user) StdoutLine: {'line': b'[ 77%] Built target planning_hpp\n'}
[2.210235] (-) TimerEvent: {}
[2.228017] (xt_user) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o\x1b[0m\n'}
[2.310402] (-) TimerEvent: {}
[2.410841] (-) TimerEvent: {}
[2.511338] (-) TimerEvent: {}
[2.611721] (-) TimerEvent: {}
[2.712087] (-) TimerEvent: {}
[2.812504] (-) TimerEvent: {}
[2.912956] (-) TimerEvent: {}
[3.013315] (-) TimerEvent: {}
[3.113759] (-) TimerEvent: {}
[3.214502] (-) TimerEvent: {}
[3.314887] (-) TimerEvent: {}
[3.415371] (-) TimerEvent: {}
[3.515744] (-) TimerEvent: {}
[3.616338] (-) TimerEvent: {}
[3.716719] (-) TimerEvent: {}
[3.817234] (-) TimerEvent: {}
[3.917566] (-) TimerEvent: {}
[4.017856] (-) TimerEvent: {}
[4.118273] (-) TimerEvent: {}
[4.218702] (-) TimerEvent: {}
[4.319072] (-) TimerEvent: {}
[4.419381] (-) TimerEvent: {}
[4.519707] (-) TimerEvent: {}
[4.620073] (-) TimerEvent: {}
[4.720463] (-) TimerEvent: {}
[4.820799] (-) TimerEvent: {}
[4.921227] (-) TimerEvent: {}
[5.021572] (-) TimerEvent: {}
[5.121975] (-) TimerEvent: {}
[5.222353] (-) TimerEvent: {}
[5.322726] (-) TimerEvent: {}
[5.423189] (-) TimerEvent: {}
[5.523648] (-) TimerEvent: {}
[5.570383] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.570645] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:182:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kpreposture\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.570790] (xt_user) StderrLine: {'line': b'  182 |     kinematic_state \x1b[01;35m\x1b[Kpreposture\x1b[m\x1b[K;\n'}
[5.570879] (xt_user) StderrLine: {'line': b'      |                     \x1b[01;35m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[5.574662] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.574796] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.574851] (xt_user) StderrLine: {'line': b'  212 |     double \x1b[01;35m\x1b[Kstime\x1b[m\x1b[K, etime;\n'}
[5.574893] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.574935] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ketime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.574975] (xt_user) StderrLine: {'line': b'  212 |     double stime, \x1b[01;35m\x1b[Ketime\x1b[m\x1b[K;\n'}
[5.575015] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.575119] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid* MapShow(void*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.575162] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:256:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.575202] (xt_user) StderrLine: {'line': b'  256 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K,end;\n'}
[5.575240] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.575276] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:256:18:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.575314] (xt_user) StderrLine: {'line': b'  256 |     double start,\x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[5.575350] (xt_user) StderrLine: {'line': b'      |                  \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.575384] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:257:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.575422] (xt_user) StderrLine: {'line': b'  257 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[5.575456] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[5.575491] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:259:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kperiod\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.575529] (xt_user) StderrLine: {'line': b'  259 |     unsigned char \x1b[01;35m\x1b[Kperiod\x1b[m\x1b[K = 0;\n'}
[5.575564] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[5.575599] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Karg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.575636] (xt_user) StderrLine: {'line': b'  254 | void* MapShow(\x1b[01;35m\x1b[Kvoid *arg\x1b[m\x1b[K)\n'}
[5.575670] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K~~~~~~^~~\x1b[m\x1b[K\n'}
[5.590065] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.590284] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.590379] (xt_user) StderrLine: {'line': b'  309 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kcommand = wp["command"].get<uint16_t>(),\n'}
[5.590447] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.595273] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.595414] (xt_user) StderrLine: {'line': b'  310 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kseq = wp["seq"].get<uint8_t>(),\n'}
[5.595489] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.602783] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.602969] (xt_user) StderrLine: {'line': b'  311 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam1 = wp["param1"].get<float>(),\n'}
[5.603046] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.606853] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.607163] (xt_user) StderrLine: {'line': b'  312 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam2 = wp["param2"].get<float>(),\n'}
[5.607420] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.607636] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.607727] (xt_user) StderrLine: {'line': b'  313 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam3 = wp["param3"].get<float>(),\n'}
[5.607848] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.607916] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:314:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.607984] (xt_user) StderrLine: {'line': b'  314 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam4 = wp["param4"].get<float>(),\n'}
[5.608043] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.608101] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:315:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.608163] (xt_user) StderrLine: {'line': b'  315 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klat = wp["x"].get<float>(),\n'}
[5.608224] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.608551] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:316:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.608643] (xt_user) StderrLine: {'line': b'  316 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klon = wp["y"].get<float>(),\n'}
[5.608707] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.608837] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:317:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.608912] (xt_user) StderrLine: {'line': b'  317 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kalt = wp["z"].get<float>()\n'}
[5.608971] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.609094] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:318:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kmissing initializer for member \xe2\x80\x98\x1b[01m\x1b[KMissionItem::reserve\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers\x07-Wmissing-field-initializers\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.609184] (xt_user) StderrLine: {'line': b'  318 |             \x1b[01;35m\x1b[K}\x1b[m\x1b[K;\n'}
[5.609246] (xt_user) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.610116] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.610219] (xt_user) StderrLine: {'line': b'  331 |     for(int i = 0; \x1b[01;35m\x1b[Ki<missions.size()\x1b[m\x1b[K; i++){\n'}
[5.610284] (xt_user) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.623803] (-) TimerEvent: {}
[5.696153] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:411:60:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunsigned conversion from \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Ku_int8_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kunsigned char\x1b[m\x1b[K\xe2\x80\x99} changes value from \xe2\x80\x98\x1b[01m\x1b[K512\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[K0\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow\x07-Woverflow\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.696335] (xt_user) StderrLine: {'line': b'  411 |                     taskAnchorMissionControl.mission_num = \x1b[01;35m\x1b[K512\x1b[m\x1b[K;\n'}
[5.696396] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.697056] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:449:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.697404] (xt_user) StderrLine: {'line': b'  449 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetError(),       // \xe5\xbd\x93\xe5\x89\x8d\xe8\xaf\xaf\xe5\xb7\xae (ERR)\n'}
[5.697478] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.697539] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.697595] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.697650] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.697704] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.697781] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:450:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.697914] (xt_user) StderrLine: {'line': b'  450 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetErrorChange(), // \xe8\xaf\xaf\xe5\xb7\xae\xe5\x8f\x98\xe5\x8c\x96 (EC)\n'}
[5.698175] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.698232] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.698272] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.698323] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.698359] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.698396] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:451:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.698449] (xt_user) StderrLine: {'line': b'  451 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetSumErr(),      // \xe7\xa7\xaf\xe5\x88\x86\xe7\xb4\xaf\xe7\xa7\xaf (SUM_ERR)\n'}
[5.698534] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.698581] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.698619] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.698662] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.698721] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.698822] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:452:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.698888] (xt_user) StderrLine: {'line': b'  452 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetKp(),          // \xe6\x9c\x80\xe7\xbb\x88Kp\n'}
[5.698958] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.699023] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.699080] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.699139] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.699204] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.699261] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:454:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.699319] (xt_user) StderrLine: {'line': b'  454 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetDeltaKi(),     // Ki\xe6\xa8\xa1\xe7\xb3\x8a\xe8\xb0\x83\xe6\x95\xb4\xe9\x87\x8f\n'}
[5.699414] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.699492] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.699566] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.699622] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.699672] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.699717] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:455:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.699817] (xt_user) StderrLine: {'line': b'  455 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetKd(),          // \xe6\x9c\x80\xe7\xbb\x88Kd\n'}
[5.699870] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.699944] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.700014] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.700076] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.700125] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.700219] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:457:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.700295] (xt_user) StderrLine: {'line': b'  457 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetDeltaKd(),     // Kd\xe6\xa8\xa1\xe7\xb3\x8a\xe8\xb0\x83\xe6\x95\xb4\xe9\x87\x8f\n'}
[5.700346] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.700395] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.700445] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.700502] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.700560] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.700616] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:459:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.700662] (xt_user) StderrLine: {'line': b'  459 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetP_Out(),       // \xe6\xaf\x94\xe4\xbe\x8b\xe9\xa1\xb9\xe8\xbe\x93\xe5\x87\xba\n'}
[5.700712] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.700810] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.700920] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.701003] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.701064] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.701122] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:460:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.701186] (xt_user) StderrLine: {'line': b'  460 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetI_Out(),       // \xe7\xa7\xaf\xe5\x88\x86\xe9\xa1\xb9\xe8\xbe\x93\xe5\x87\xba\n'}
[5.701240] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.701292] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.701343] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.701388] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.701433] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.701482] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:461:32:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KFuzzyPIDControl MotionControl::fuzzyheadingcontroller\x1b[m\x1b[K\xe2\x80\x99 is private within this context\n'}
[5.701524] (xt_user) StderrLine: {'line': b'  461 |                     controller.\x1b[01;31m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K.GetD_Out()        // \xe5\xbe\xae\xe5\x88\x86\xe9\xa1\xb9\xe8\xbe\x93\xe5\x87\xba\n'}
[5.701576] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.701615] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:42\x1b[m\x1b[K:\n'}
[5.701658] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/../controller/motion_control.h:30:25:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kdeclared private here\n'}
[5.701694] (xt_user) StderrLine: {'line': b'   30 |         FuzzyPIDControl \x1b[01;36m\x1b[Kfuzzyheadingcontroller\x1b[m\x1b[K;\n'}
[5.701789] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.702708] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:335:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.702836] (xt_user) StderrLine: {'line': b'  335 |     double start, \x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[5.702886] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.702926] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:336:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kduration\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.702967] (xt_user) StderrLine: {'line': b'  336 |     double \x1b[01;35m\x1b[Kduration\x1b[m\x1b[K[10];\n'}
[5.703005] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.703040] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:337:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.703079] (xt_user) StderrLine: {'line': b'  337 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[5.703114] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[5.703150] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:390:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kanglevel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.703188] (xt_user) StderrLine: {'line': b'  390 |     double \x1b[01;35m\x1b[Kanglevel\x1b[m\x1b[K[2];\n'}
[5.703227] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.723988] (-) TimerEvent: {}
[5.824357] (-) TimerEvent: {}
[5.924762] (-) TimerEvent: {}
[6.025297] (-) TimerEvent: {}
[6.125728] (-) TimerEvent: {}
[6.226108] (-) TimerEvent: {}
[6.326531] (-) TimerEvent: {}
[6.426843] (-) TimerEvent: {}
[6.527160] (-) TimerEvent: {}
[6.627502] (-) TimerEvent: {}
[6.727835] (-) TimerEvent: {}
[6.828177] (-) TimerEvent: {}
[6.928506] (-) TimerEvent: {}
[7.028896] (-) TimerEvent: {}
[7.129452] (-) TimerEvent: {}
[7.229797] (-) TimerEvent: {}
[7.330295] (-) TimerEvent: {}
[7.430769] (-) TimerEvent: {}
[7.531275] (-) TimerEvent: {}
[7.631659] (-) TimerEvent: {}
[7.732126] (-) TimerEvent: {}
[7.832471] (-) TimerEvent: {}
[7.933328] (-) TimerEvent: {}
[8.033705] (-) TimerEvent: {}
[8.134077] (-) TimerEvent: {}
[8.234421] (-) TimerEvent: {}
[8.334793] (-) TimerEvent: {}
[8.435242] (-) TimerEvent: {}
[8.535687] (-) TimerEvent: {}
[8.636028] (-) TimerEvent: {}
[8.736313] (-) TimerEvent: {}
[8.836714] (-) TimerEvent: {}
[8.937062] (-) TimerEvent: {}
[9.037349] (-) TimerEvent: {}
[9.137651] (-) TimerEvent: {}
[9.238075] (-) TimerEvent: {}
[9.338486] (-) TimerEvent: {}
[9.438810] (-) TimerEvent: {}
[9.529510] (xt_user) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/xt_user_node.dir/build.make:76\xef\xbc\x9aCMakeFiles/xt_user_node.dir/main/planning_main.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[9.529799] (xt_user) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:165\xef\xbc\x9aCMakeFiles/xt_user_node.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[9.530020] (xt_user) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[9.532774] (xt_user) CommandEnded: {'returncode': 2}
[9.539064] (-) TimerEvent: {}
[9.539651] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 2}
[9.550285] (-) EventReactorShutdown: {}
