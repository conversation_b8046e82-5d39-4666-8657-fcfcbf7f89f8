-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found OpenCV: /usr (found version "4.5.4") 
-- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") 
-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system 
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
[ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o[0m
[ 22%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o[0m
[ 33%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o[0m
[ 44%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o[0m
[ 55%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[ 66%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:271:15:[m[K [01;31m[Kerror: [m[K‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’ cannot be overloaded with ‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’
  271 |         float [01;31m[KCalculateLOSAngle[m[K(float currentPos[2], float targetPos[2], float currentHeading);
      |               [01;31m[K^~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:239:15:[m[K [01;36m[Knote: [m[Kprevious declaration ‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’
  239 |         float [01;36m[KCalculateLOSAngle[m[K(float currentPos[2], float targetPos[2], float currentHeading);
      |               [01;36m[K^~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:38:[m[K [01;31m[Kerror: [m[K‘[01m[Kstring[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
  314 |         void LogDebugInfo(const std::[01;31m[Kstring[m[K& message);
      |                                      [01;31m[K^~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:10:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::string[m[K’ is defined in header ‘[01m[K<string>[m[K’; did you forget to ‘[01m[K#include <string>[m[K’?
    9 | #include "../../include/boat_datadef.h"
  +++ |+[32m[K#include <string>[m[K
   10 | 
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Knowpos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  347 |         double [01;35m[Knowpos[m[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
      |                [01;35m[K^~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:51:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  347 |         double nowpos[2] = {0}, homepos[2] = {0}, [01;35m[Klengthangle[m[K[2] = {0};
      |                                                   [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState, std::vector<std::vector<int> >*)[m[K’:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:509:46:[m[K [01;31m[Kerror: [m[Kinvalid conversion from ‘[01m[Kchar*[m[K’ to ‘[01m[Kint[m[K’ [[01;31m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive-fpermissive]8;;[m[K]
  509 |                                 LogDebugInfo([01;31m[KdebugMsg[m[K);
      |                                              [01;31m[K^~~~~~~~[m[K
      |                                              [01;31m[K|[m[K
      |                                              [01;31m[Kchar*[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:[m[K [01;36m[Knote: [m[K  initializing argument 1 of ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
  314 |         void LogDebugInfo([01;36m[Kconst std::string& message[m[K);
      |                           [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:673:46:[m[K [01;31m[Kerror: [m[Kinvalid conversion from ‘[01m[Kchar*[m[K’ to ‘[01m[Kint[m[K’ [[01;31m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive-fpermissive]8;;[m[K]
  673 |                                 LogDebugInfo([01;31m[KdebugMsg[m[K);
      |                                              [01;31m[K^~~~~~~~[m[K
      |                                              [01;31m[K|[m[K
      |                                              [01;31m[Kchar*[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:[m[K [01;36m[Knote: [m[K  initializing argument 1 of ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
  314 |         void LogDebugInfo([01;36m[Kconst std::string& message[m[K);
      |                           [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:601:23:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kpretovel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  601 |                 float [01;35m[Kpretovel[m[K[2] = {0};
      |                       [01;35m[K^~~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:474:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  474 |         float [01;35m[Klengthangle[m[K[2] = {0}, vel = 0;    // 长度角度和速度变量
      |               [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K At global scope:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:885:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid MotionPlan::LogDebugInfo(const string&)[m[K’
  885 | void [01;31m[KMotionPlan[m[K::LogDebugInfo(const std::string& message)
      |      [01;31m[K^~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:14:[m[K [01;36m[Knote: [m[Kcandidate is: ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
  314 |         void [01;36m[KLogDebugInfo[m[K(const std::string& message);
      |              [01;36m[K^~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:54:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass MotionPlan[m[K’ defined here
   54 | class [01;36m[KMotionPlan[m[K
      |       [01;36m[K^~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90：CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/planning_hpp.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
