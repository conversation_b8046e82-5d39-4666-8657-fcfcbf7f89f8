[0.044s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user
[0.283s] -- The C compiler identification is GNU 11.4.0
[0.431s] -- The CXX compiler identification is GNU 11.4.0
[0.463s] -- Detecting C compiler ABI info
[0.654s] -- Detecting C compiler ABI info - done
[0.672s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.674s] -- Detecting C compile features
[0.675s] -- Detecting C compile features - done
[0.682s] -- Detecting CXX compiler ABI info
[0.850s] -- Detecting CXX compiler ABI info - done
[0.866s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.867s] -- Detecting CXX compile features
[0.867s] -- Detecting CXX compile features - done
[0.880s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[1.128s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[1.334s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[1.442s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[1.454s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[1.477s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[1.512s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.541s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[1.642s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[1.648s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.842s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[1.888s] -- Found FastRTPS: /opt/ros/humble/include  
[1.952s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.972s] -- Looking for pthread.h
[2.152s] -- Looking for pthread.h - found
[2.152s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[2.287s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[2.288s] -- Found Threads: TRUE  
[2.419s] -- Found OpenCV: /usr (found version "4.5.4") 
[2.428s] -- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") 
[2.432s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[2.542s] -- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system 
[2.662s] -- Configuring done
[2.692s] -- Generating done
[2.708s] -- Build files have been written to: /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
[2.732s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user
[2.736s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[2.852s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o[0m
[3.158s] [ 22%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o[0m
[3.436s] [ 33%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o[0m
[4.333s] [ 44%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o[0m
[5.061s] [ 55%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[5.961s] [ 66%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[6.249s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.249s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:271:15:[m[K [01;31m[Kerror: [m[K‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’ cannot be overloaded with ‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’
[6.249s]   271 |         float [01;31m[KCalculateLOSAngle[m[K(float currentPos[2], float targetPos[2], float currentHeading);
[6.249s]       |               [01;31m[K^~~~~~~~~~~~~~~~~[m[K
[6.249s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.249s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:239:15:[m[K [01;36m[Knote: [m[Kprevious declaration ‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’
[6.249s]   239 |         float [01;36m[KCalculateLOSAngle[m[K(float currentPos[2], float targetPos[2], float currentHeading);
[6.250s]       |               [01;36m[K^~~~~~~~~~~~~~~~~[m[K
[6.250s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.250s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:38:[m[K [01;31m[Kerror: [m[K‘[01m[Kstring[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
[6.250s]   314 |         void LogDebugInfo(const std::[01;31m[Kstring[m[K& message);
[6.250s]       |                                      [01;31m[K^~~~~~[m[K
[6.250s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.250s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:10:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::string[m[K’ is defined in header ‘[01m[K<string>[m[K’; did you forget to ‘[01m[K#include <string>[m[K’?
[6.250s]     9 | #include "../../include/boat_datadef.h"
[6.250s]   +++ |+[32m[K#include <string>[m[K
[6.250s]    10 | 
[6.638s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[6.638s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Knowpos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.638s]   347 |         double [01;35m[Knowpos[m[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
[6.638s]       |                [01;35m[K^~~~~~[m[K
[6.638s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:51:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.638s]   347 |         double nowpos[2] = {0}, homepos[2] = {0}, [01;35m[Klengthangle[m[K[2] = {0};
[6.638s]       |                                                   [01;35m[K^~~~~~~~~~~[m[K
[6.639s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState, std::vector<std::vector<int> >*)[m[K’:
[6.639s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:509:46:[m[K [01;31m[Kerror: [m[Kinvalid conversion from ‘[01m[Kchar*[m[K’ to ‘[01m[Kint[m[K’ [[01;31m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive-fpermissive]8;;[m[K]
[6.640s]   509 |                                 LogDebugInfo([01;31m[KdebugMsg[m[K);
[6.640s]       |                                              [01;31m[K^~~~~~~~[m[K
[6.640s]       |                                              [01;31m[K|[m[K
[6.640s]       |                                              [01;31m[Kchar*[m[K
[6.645s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.645s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:[m[K [01;36m[Knote: [m[K  initializing argument 1 of ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
[6.645s]   314 |         void LogDebugInfo([01;36m[Kconst std::string& message[m[K);
[6.645s]       |                           [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[6.645s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:673:46:[m[K [01;31m[Kerror: [m[Kinvalid conversion from ‘[01m[Kchar*[m[K’ to ‘[01m[Kint[m[K’ [[01;31m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive-fpermissive]8;;[m[K]
[6.645s]   673 |                                 LogDebugInfo([01;31m[KdebugMsg[m[K);
[6.646s]       |                                              [01;31m[K^~~~~~~~[m[K
[6.646s]       |                                              [01;31m[K|[m[K
[6.646s]       |                                              [01;31m[Kchar*[m[K
[6.646s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.646s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:[m[K [01;36m[Knote: [m[K  initializing argument 1 of ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
[6.646s]   314 |         void LogDebugInfo([01;36m[Kconst std::string& message[m[K);
[6.646s]       |                           [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[6.646s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:601:23:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kpretovel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.646s]   601 |                 float [01;35m[Kpretovel[m[K[2] = {0};
[6.646s]       |                       [01;35m[K^~~~~~~~[m[K
[6.646s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:474:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.646s]   474 |         float [01;35m[Klengthangle[m[K[2] = {0}, vel = 0;    // 长度角度和速度变量
[6.646s]       |               [01;35m[K^~~~~~~~~~~[m[K
[6.652s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K At global scope:
[6.653s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:885:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid MotionPlan::LogDebugInfo(const string&)[m[K’
[6.653s]   885 | void [01;31m[KMotionPlan[m[K::LogDebugInfo(const std::string& message)
[6.653s]       |      [01;31m[K^~~~~~~~~~[m[K
[6.653s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.653s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:14:[m[K [01;36m[Knote: [m[Kcandidate is: ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
[6.653s]   314 |         void [01;36m[KLogDebugInfo[m[K(const std::string& message);
[6.653s]       |              [01;36m[K^~~~~~~~~~~~[m[K
[6.653s] In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[6.653s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:54:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass MotionPlan[m[K’ defined here
[6.653s]    54 | class [01;36m[KMotionPlan[m[K
[6.653s]       |       [01;36m[K^~~~~~~~~~[m[K
[6.787s] gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90：CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] 错误 1
[6.788s] gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/planning_hpp.dir/all] 错误 2
[6.791s] gmake: *** [Makefile:146：all] 错误 2
[6.802s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
