Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user
Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user
Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
