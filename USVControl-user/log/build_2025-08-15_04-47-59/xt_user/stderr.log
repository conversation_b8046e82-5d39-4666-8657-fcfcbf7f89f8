In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:271:15:[m[K [01;31m[Kerror: [m[K‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’ cannot be overloaded with ‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’
  271 |         float [01;31m[KCalculateLOSAngle[m[K(float currentPos[2], float targetPos[2], float currentHeading);
      |               [01;31m[K^~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:239:15:[m[K [01;36m[Knote: [m[Kprevious declaration ‘[01m[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)[m[K’
  239 |         float [01;36m[KCalculateLOSAngle[m[K(float currentPos[2], float targetPos[2], float currentHeading);
      |               [01;36m[K^~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:38:[m[K [01;31m[Kerror: [m[K‘[01m[Kstring[m[K’ in namespace ‘[01m[Kstd[m[K’ does not name a type
  314 |         void LogDebugInfo(const std::[01;31m[Kstring[m[K& message);
      |                                      [01;31m[K^~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:10:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstd::string[m[K’ is defined in header ‘[01m[K<string>[m[K’; did you forget to ‘[01m[K#include <string>[m[K’?
    9 | #include "../../include/boat_datadef.h"
  +++ |+[32m[K#include <string>[m[K
   10 | 
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Knowpos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  347 |         double [01;35m[Knowpos[m[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
      |                [01;35m[K^~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:51:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  347 |         double nowpos[2] = {0}, homepos[2] = {0}, [01;35m[Klengthangle[m[K[2] = {0};
      |                                                   [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState, std::vector<std::vector<int> >*)[m[K’:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:509:46:[m[K [01;31m[Kerror: [m[Kinvalid conversion from ‘[01m[Kchar*[m[K’ to ‘[01m[Kint[m[K’ [[01;31m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive-fpermissive]8;;[m[K]
  509 |                                 LogDebugInfo([01;31m[KdebugMsg[m[K);
      |                                              [01;31m[K^~~~~~~~[m[K
      |                                              [01;31m[K|[m[K
      |                                              [01;31m[Kchar*[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:[m[K [01;36m[Knote: [m[K  initializing argument 1 of ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
  314 |         void LogDebugInfo([01;36m[Kconst std::string& message[m[K);
      |                           [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:673:46:[m[K [01;31m[Kerror: [m[Kinvalid conversion from ‘[01m[Kchar*[m[K’ to ‘[01m[Kint[m[K’ [[01;31m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive-fpermissive]8;;[m[K]
  673 |                                 LogDebugInfo([01;31m[KdebugMsg[m[K);
      |                                              [01;31m[K^~~~~~~~[m[K
      |                                              [01;31m[K|[m[K
      |                                              [01;31m[Kchar*[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:[m[K [01;36m[Knote: [m[K  initializing argument 1 of ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
  314 |         void LogDebugInfo([01;36m[Kconst std::string& message[m[K);
      |                           [01;36m[K~~~~~~~~~~~~~~~~~~~^~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:601:23:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kpretovel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  601 |                 float [01;35m[Kpretovel[m[K[2] = {0};
      |                       [01;35m[K^~~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:474:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  474 |         float [01;35m[Klengthangle[m[K[2] = {0}, vel = 0;    // 长度角度和速度变量
      |               [01;35m[K^~~~~~~~~~~[m[K
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K At global scope:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:885:6:[m[K [01;31m[Kerror: [m[Kno declaration matches ‘[01m[Kvoid MotionPlan::LogDebugInfo(const string&)[m[K’
  885 | void [01;31m[KMotionPlan[m[K::LogDebugInfo(const std::string& message)
      |      [01;31m[K^~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:14:[m[K [01;36m[Knote: [m[Kcandidate is: ‘[01m[Kvoid MotionPlan::LogDebugInfo(const int&)[m[K’
  314 |         void [01;36m[KLogDebugInfo[m[K(const std::string& message);
      |              [01;36m[K^~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:54:7:[m[K [01;36m[Knote: [m[K‘[01m[Kclass MotionPlan[m[K’ defined here
   54 | class [01;36m[KMotionPlan[m[K
      |       [01;36m[K^~~~~~~~~~[m[K
gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90：CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/planning_hpp.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
