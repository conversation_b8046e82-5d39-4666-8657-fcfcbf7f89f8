[0.185s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'xt_user']
[0.185s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['xt_user'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7ab618a23280>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7ab618b68790>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7ab618b68790>>)
[0.428s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.429s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.430s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/333/project2_0815/USVControl-user'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.457s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['ignore', 'ignore_ament_install']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'ignore'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'ignore_ament_install'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['colcon_pkg']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'colcon_pkg'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['colcon_meta']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'colcon_meta'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['ros']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'ros'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['cmake', 'python']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'cmake'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'python'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['python_setup_py']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'python_setup_py'
[0.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.460s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.491s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.491s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.494s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 271 installed packages in /opt/ros/humble
[0.497s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.575s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.575s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.575s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.576s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.576s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.576s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.576s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.576s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.576s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.576s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.576s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.583s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.584s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.585s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning'
[0.589s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.589s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.589s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.632s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user
[3.319s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user
[3.324s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[7.387s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[7.389s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[7.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user' for CMake module files
[7.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user' for CMake config files
[7.396s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[7.397s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[7.398s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[7.398s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[7.400s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib'
[7.400s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[7.401s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[7.402s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[7.403s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[7.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/bin'
[7.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[7.403s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[7.404s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/bin'
[7.406s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[7.407s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[7.408s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.sh'
[7.409s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.bash'
[7.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[7.416s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[7.418s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[7.418s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[7.418s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[7.418s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[7.427s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[7.427s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[7.427s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[7.463s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[7.465s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.ps1'
[7.469s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/333/project2_0815/USVControl-user/install/_local_setup_util_ps1.py'
[7.477s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.ps1'
[7.480s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.sh'
[7.482s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/333/project2_0815/USVControl-user/install/_local_setup_util_sh.py'
[7.483s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.sh'
[7.484s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.bash'
[7.485s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.bash'
[7.491s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.zsh'
[7.492s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.zsh'
