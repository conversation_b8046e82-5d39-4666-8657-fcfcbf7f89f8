[0.000000] (-) TimerEvent: {}
[0.005536] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.008816] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.044328] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.053940] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/333/project2_0815/USVControl-user/install/xt_user'], 'cwd': '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/333/project2_0815'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1569'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1711'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '18393'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:22143'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1711,unix/xumj-virtual-machine:/tmp/.ICE-unix/1711'), ('INVOCATION_ID', '3d9646cf809448b2ab7517905cc62e8f'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.2XD9A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1d51052266.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.102456] (-) TimerEvent: {}
[0.204105] (-) TimerEvent: {}
[0.293580] (xt_user) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.308337] (-) TimerEvent: {}
[0.408948] (-) TimerEvent: {}
[0.439891] (xt_user) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.473720] (xt_user) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.510537] (-) TimerEvent: {}
[0.611931] (-) TimerEvent: {}
[0.664376] (xt_user) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.682237] (xt_user) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.684197] (xt_user) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.684943] (xt_user) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.692455] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.713425] (-) TimerEvent: {}
[0.815104] (-) TimerEvent: {}
[0.859983] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.876030] (xt_user) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.876917] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.877013] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.890676] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.916349] (-) TimerEvent: {}
[1.020948] (-) TimerEvent: {}
[1.121739] (-) TimerEvent: {}
[1.135820] (xt_user) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[1.222037] (-) TimerEvent: {}
[1.324473] (-) TimerEvent: {}
[1.344654] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[1.425477] (-) TimerEvent: {}
[1.451986] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[1.464500] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[1.487608] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[1.521827] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.525565] (-) TimerEvent: {}
[1.551554] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.628047] (-) TimerEvent: {}
[1.652385] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.658455] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.728511] (-) TimerEvent: {}
[1.828947] (-) TimerEvent: {}
[1.851961] (xt_user) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[1.898539] (xt_user) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[1.929610] (-) TimerEvent: {}
[1.962471] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.982541] (xt_user) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[2.032988] (-) TimerEvent: {}
[2.134880] (-) TimerEvent: {}
[2.162322] (xt_user) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[2.162551] (xt_user) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[2.237371] (-) TimerEvent: {}
[2.296977] (xt_user) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[2.298191] (xt_user) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[2.338729] (-) TimerEvent: {}
[2.429621] (xt_user) StdoutLine: {'line': b'-- Found OpenCV: /usr (found version "4.5.4") \n'}
[2.437730] (xt_user) StdoutLine: {'line': b'-- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") \n'}
[2.441091] (-) TimerEvent: {}
[2.442032] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[2.541665] (-) TimerEvent: {}
[2.552084] (xt_user) StdoutLine: {'line': b'-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system \n'}
[2.642330] (-) TimerEvent: {}
[2.672016] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[2.702090] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[2.717134] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/333/project2_0815/USVControl-user/build/xt_user\n'}
[2.741833] (xt_user) CommandEnded: {'returncode': 0}
[2.742439] (-) TimerEvent: {}
[2.746248] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[2.746401] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/333/project2_0815'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1569'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1711'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '18393'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:22143'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1711,unix/xumj-virtual-machine:/tmp/.ICE-unix/1711'), ('INVOCATION_ID', '3d9646cf809448b2ab7517905cc62e8f'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.2XD9A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1d51052266.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[2.843001] (-) TimerEvent: {}
[2.862550] (xt_user) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/333/project2_0815/USVControl-user/ControlNode/include/boat_datadef.cpp.o\x1b[0m\n'}
[2.943604] (-) TimerEvent: {}
[3.044408] (-) TimerEvent: {}
[3.151214] (-) TimerEvent: {}
[3.168196] (xt_user) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/smc_control.cpp.o\x1b[0m\n'}
[3.251569] (-) TimerEvent: {}
[3.352106] (-) TimerEvent: {}
[3.446573] (xt_user) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/fuzzy_pid_control.cpp.o\x1b[0m\n'}
[3.456280] (-) TimerEvent: {}
[3.557491] (-) TimerEvent: {}
[3.660314] (-) TimerEvent: {}
[3.761078] (-) TimerEvent: {}
[3.861958] (-) TimerEvent: {}
[3.962945] (-) TimerEvent: {}
[4.066351] (-) TimerEvent: {}
[4.167895] (-) TimerEvent: {}
[4.269990] (-) TimerEvent: {}
[4.342860] (xt_user) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o\x1b[0m\n'}
[4.372501] (-) TimerEvent: {}
[4.473307] (-) TimerEvent: {}
[4.575559] (-) TimerEvent: {}
[4.676610] (-) TimerEvent: {}
[4.778004] (-) TimerEvent: {}
[4.878598] (-) TimerEvent: {}
[4.979306] (-) TimerEvent: {}
[5.071543] (xt_user) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o\x1b[0m\n'}
[5.079848] (-) TimerEvent: {}
[5.185500] (-) TimerEvent: {}
[5.288859] (-) TimerEvent: {}
[5.390396] (-) TimerEvent: {}
[5.491276] (-) TimerEvent: {}
[5.592337] (-) TimerEvent: {}
[5.699825] (-) TimerEvent: {}
[5.800872] (-) TimerEvent: {}
[5.903680] (-) TimerEvent: {}
[5.971549] (xt_user) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[6.004340] (-) TimerEvent: {}
[6.104997] (-) TimerEvent: {}
[6.207229] (-) TimerEvent: {}
[6.259025] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.259448] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:271:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99 cannot be overloaded with \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.259575] (xt_user) StderrLine: {'line': b'  271 |         float \x1b[01;31m\x1b[KCalculateLOSAngle\x1b[m\x1b[K(float currentPos[2], float targetPos[2], float currentHeading);\n'}
[6.259655] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[6.259726] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.259794] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:239:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kprevious declaration \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateLOSAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.259866] (xt_user) StderrLine: {'line': b'  239 |         float \x1b[01;36m\x1b[KCalculateLOSAngle\x1b[m\x1b[K(float currentPos[2], float targetPos[2], float currentHeading);\n'}
[6.259933] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[6.260122] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.260207] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:38:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstring\x1b[m\x1b[K\xe2\x80\x99 in namespace \xe2\x80\x98\x1b[01m\x1b[Kstd\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[6.260289] (xt_user) StderrLine: {'line': b'  314 |         void LogDebugInfo(const std::\x1b[01;31m\x1b[Kstring\x1b[m\x1b[K& message);\n'}
[6.260358] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;31m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[6.260425] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.260492] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:10:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstd::string\x1b[m\x1b[K\xe2\x80\x99 is defined in header \xe2\x80\x98\x1b[01m\x1b[K<string>\x1b[m\x1b[K\xe2\x80\x99; did you forget to \xe2\x80\x98\x1b[01m\x1b[K#include <string>\x1b[m\x1b[K\xe2\x80\x99?\n'}
[6.260563] (xt_user) StderrLine: {'line': b'    9 | #include "../../include/boat_datadef.h"\n'}
[6.260630] (xt_user) StderrLine: {'line': b'  +++ |+\x1b[32m\x1b[K#include <string>\x1b[m\x1b[K\n'}
[6.260703] (xt_user) StderrLine: {'line': b'   10 | \n'}
[6.308237] (-) TimerEvent: {}
[6.416706] (-) TimerEvent: {}
[6.517506] (-) TimerEvent: {}
[6.620910] (-) TimerEvent: {}
[6.647813] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.648087] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Knowpos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.648161] (xt_user) StderrLine: {'line': b'  347 |         double \x1b[01;35m\x1b[Knowpos\x1b[m\x1b[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};\n'}
[6.648221] (xt_user) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[6.648279] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:51:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Klengthangle\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.648338] (xt_user) StderrLine: {'line': b'  347 |         double nowpos[2] = {0}, homepos[2] = {0}, \x1b[01;35m\x1b[Klengthangle\x1b[m\x1b[K[2] = {0};\n'}
[6.648394] (xt_user) StderrLine: {'line': b'      |                                                   \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[6.649610] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState, std::vector<std::vector<int> >*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.649824] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:509:46:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kinvalid conversion from \xe2\x80\x98\x1b[01m\x1b[Kchar*\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;31m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive\x07-fpermissive\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.649916] (xt_user) StderrLine: {'line': b'  509 |                                 LogDebugInfo(\x1b[01;31m\x1b[KdebugMsg\x1b[m\x1b[K);\n'}
[6.649990] (xt_user) StderrLine: {'line': b'      |                                              \x1b[01;31m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.650058] (xt_user) StderrLine: {'line': b'      |                                              \x1b[01;31m\x1b[K|\x1b[m\x1b[K\n'}
[6.650131] (xt_user) StderrLine: {'line': b'      |                                              \x1b[01;31m\x1b[Kchar*\x1b[m\x1b[K\n'}
[6.652632] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.655630] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  initializing argument 1 of \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::LogDebugInfo(const int&)\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.655708] (xt_user) StderrLine: {'line': b'  314 |         void LogDebugInfo(\x1b[01;36m\x1b[Kconst std::string& message\x1b[m\x1b[K);\n'}
[6.655768] (xt_user) StderrLine: {'line': b'      |                           \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~~~^~~~~~~\x1b[m\x1b[K\n'}
[6.655839] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:673:46:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kinvalid conversion from \xe2\x80\x98\x1b[01m\x1b[Kchar*\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;31m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-fpermissive\x07-fpermissive\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.655902] (xt_user) StderrLine: {'line': b'  673 |                                 LogDebugInfo(\x1b[01;31m\x1b[KdebugMsg\x1b[m\x1b[K);\n'}
[6.655959] (xt_user) StderrLine: {'line': b'      |                                              \x1b[01;31m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.656015] (xt_user) StderrLine: {'line': b'      |                                              \x1b[01;31m\x1b[K|\x1b[m\x1b[K\n'}
[6.656104] (xt_user) StderrLine: {'line': b'      |                                              \x1b[01;31m\x1b[Kchar*\x1b[m\x1b[K\n'}
[6.656166] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.656226] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:46:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  initializing argument 1 of \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::LogDebugInfo(const int&)\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.656285] (xt_user) StderrLine: {'line': b'  314 |         void LogDebugInfo(\x1b[01;36m\x1b[Kconst std::string& message\x1b[m\x1b[K);\n'}
[6.656341] (xt_user) StderrLine: {'line': b'      |                           \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~~~^~~~~~~\x1b[m\x1b[K\n'}
[6.656398] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:601:23:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kpretovel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.656462] (xt_user) StderrLine: {'line': b'  601 |                 float \x1b[01;35m\x1b[Kpretovel\x1b[m\x1b[K[2] = {0};\n'}
[6.656521] (xt_user) StderrLine: {'line': b'      |                       \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.656581] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:474:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Klengthangle\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.656648] (xt_user) StderrLine: {'line': b'  474 |         float \x1b[01;35m\x1b[Klengthangle\x1b[m\x1b[K[2] = {0}, vel = 0;    // \xe9\x95\xbf\xe5\xba\xa6\xe8\xa7\x92\xe5\xba\xa6\xe5\x92\x8c\xe9\x80\x9f\xe5\xba\xa6\xe5\x8f\x98\xe9\x87\x8f\n'}
[6.656710] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[6.662576] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K At global scope:\n'}
[6.662976] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:885:6:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno declaration matches \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::LogDebugInfo(const string&)\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.663064] (xt_user) StderrLine: {'line': b'  885 | void \x1b[01;31m\x1b[KMotionPlan\x1b[m\x1b[K::LogDebugInfo(const std::string& message)\n'}
[6.663135] (xt_user) StderrLine: {'line': b'      |      \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[6.663308] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.663432] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:314:14:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate is: \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::LogDebugInfo(const int&)\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.663513] (xt_user) StderrLine: {'line': b'  314 |         void \x1b[01;36m\x1b[KLogDebugInfo\x1b[m\x1b[K(const std::string& message);\n'}
[6.663593] (xt_user) StderrLine: {'line': b'      |              \x1b[01;36m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[6.663673] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[6.663747] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:54:7:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass MotionPlan\x1b[m\x1b[K\xe2\x80\x99 defined here\n'}
[6.663826] (xt_user) StderrLine: {'line': b'   54 | class \x1b[01;36m\x1b[KMotionPlan\x1b[m\x1b[K\n'}
[6.663896] (xt_user) StderrLine: {'line': b'      |       \x1b[01;36m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[6.721779] (-) TimerEvent: {}
[6.797292] (xt_user) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90\xef\xbc\x9aCMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[6.798006] (xt_user) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:139\xef\xbc\x9aCMakeFiles/planning_hpp.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[6.801461] (xt_user) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[6.811752] (xt_user) CommandEnded: {'returncode': 2}
[6.822141] (-) TimerEvent: {}
[6.839453] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 2}
[6.840774] (-) EventReactorShutdown: {}
