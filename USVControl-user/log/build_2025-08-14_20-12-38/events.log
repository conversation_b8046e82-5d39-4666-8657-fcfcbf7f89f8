[0.000000] (-) TimerEvent: {}
[0.000703] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.001392] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.010029] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.011056] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user'], 'cwd': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'haique'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1916'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haique'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haique'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/haique:@/tmp/.ICE-unix/1916,unix/haique:/tmp/.ICE-unix/1916'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ee699f72_6152_4c72_b626_1c2c2fa14a12'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.F70RA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble')]), 'shell': False}
[0.026537] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.098789] (-) TimerEvent: {}
[0.133114] (xt_user) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.142655] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.163694] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.166135] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.171189] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.181540] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.192596] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.198918] (-) TimerEvent: {}
[0.219287] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.220369] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.299055] (-) TimerEvent: {}
[0.307590] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.355515] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[0.399158] (-) TimerEvent: {}
[0.447072] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[0.464614] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[0.470593] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user\n'}
[0.477504] (xt_user) CommandEnded: {'returncode': 0}
[0.478207] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[0.478713] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'haique'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1916'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haique'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haique'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/haique:@/tmp/.ICE-unix/1916,unix/haique:/tmp/.ICE-unix/1916'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ee699f72_6152_4c72_b626_1c2c2fa14a12'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.F70RA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble')]), 'shell': False}
[0.499290] (-) TimerEvent: {}
[0.524922] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target planning_hpp\x1b[0m\n'}
[0.543209] (xt_user) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o\x1b[0m\n'}
[0.599431] (-) TimerEvent: {}
[0.699806] (-) TimerEvent: {}
[0.800258] (-) TimerEvent: {}
[0.900669] (-) TimerEvent: {}
[1.001187] (-) TimerEvent: {}
[1.101632] (-) TimerEvent: {}
[1.106006] (xt_user) StdoutLine: {'line': b'[ 22%] \x1b[32m\x1b[1mLinking CXX shared library libplanning_hpp.so\x1b[0m\n'}
[1.201762] (-) TimerEvent: {}
[1.216608] (xt_user) StdoutLine: {'line': b'[ 77%] Built target planning_hpp\n'}
[1.227269] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target xt_user_node\x1b[0m\n'}
[1.252013] (xt_user) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o\x1b[0m\n'}
[1.301926] (-) TimerEvent: {}
[1.402310] (-) TimerEvent: {}
[1.502724] (-) TimerEvent: {}
[1.603120] (-) TimerEvent: {}
[1.703615] (-) TimerEvent: {}
[1.804122] (-) TimerEvent: {}
[1.904662] (-) TimerEvent: {}
[2.005066] (-) TimerEvent: {}
[2.105440] (-) TimerEvent: {}
[2.205799] (-) TimerEvent: {}
[2.306161] (-) TimerEvent: {}
[2.406752] (-) TimerEvent: {}
[2.507136] (-) TimerEvent: {}
[2.607705] (-) TimerEvent: {}
[2.708115] (-) TimerEvent: {}
[2.808580] (-) TimerEvent: {}
[2.908933] (-) TimerEvent: {}
[3.009356] (-) TimerEvent: {}
[3.109842] (-) TimerEvent: {}
[3.210240] (-) TimerEvent: {}
[3.310732] (-) TimerEvent: {}
[3.411104] (-) TimerEvent: {}
[3.511502] (-) TimerEvent: {}
[3.612101] (-) TimerEvent: {}
[3.712519] (-) TimerEvent: {}
[3.812863] (-) TimerEvent: {}
[3.913321] (-) TimerEvent: {}
[4.013781] (-) TimerEvent: {}
[4.114145] (-) TimerEvent: {}
[4.214521] (-) TimerEvent: {}
[4.315089] (-) TimerEvent: {}
[4.415530] (-) TimerEvent: {}
[4.515879] (-) TimerEvent: {}
[4.616352] (-) TimerEvent: {}
[4.619937] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.620184] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:177:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kpreposture\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.620272] (xt_user) StderrLine: {'line': b'  177 |     kinematic_state \x1b[01;35m\x1b[Kpreposture\x1b[m\x1b[K;\n'}
[4.620403] (xt_user) StderrLine: {'line': b'      |                     \x1b[01;35m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[4.624116] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.624443] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.624532] (xt_user) StderrLine: {'line': b'  207 |     double \x1b[01;35m\x1b[Kstime\x1b[m\x1b[K, etime;\n'}
[4.624592] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.624646] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ketime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.624782] (xt_user) StderrLine: {'line': b'  207 |     double stime, \x1b[01;35m\x1b[Ketime\x1b[m\x1b[K;\n'}
[4.624925] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.625172] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid* MapShow(void*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.625240] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.625325] (xt_user) StderrLine: {'line': b'  251 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K,end;\n'}
[4.625391] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.625446] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:18:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.625508] (xt_user) StderrLine: {'line': b'  251 |     double start,\x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[4.625561] (xt_user) StderrLine: {'line': b'      |                  \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[4.625615] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:252:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.625685] (xt_user) StderrLine: {'line': b'  252 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[4.625770] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[4.625856] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kperiod\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.625952] (xt_user) StderrLine: {'line': b'  254 |     unsigned char \x1b[01;35m\x1b[Kperiod\x1b[m\x1b[K = 0;\n'}
[4.626040] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[4.626149] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:249:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Karg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.626279] (xt_user) StderrLine: {'line': b'  249 | void* MapShow(\x1b[01;35m\x1b[Kvoid *arg\x1b[m\x1b[K)\n'}
[4.626378] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K~~~~~~^~~\x1b[m\x1b[K\n'}
[4.642153] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.642492] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.642589] (xt_user) StderrLine: {'line': b'  304 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kcommand = wp["command"].get<uint16_t>(),\n'}
[4.642653] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.647901] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.648110] (xt_user) StderrLine: {'line': b'  305 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kseq = wp["seq"].get<uint8_t>(),\n'}
[4.648185] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.652370] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:306:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.652556] (xt_user) StderrLine: {'line': b'  306 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam1 = wp["param1"].get<float>(),\n'}
[4.652628] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.656475] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.656739] (xt_user) StderrLine: {'line': b'  307 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam2 = wp["param2"].get<float>(),\n'}
[4.656848] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.657051] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:308:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.657172] (xt_user) StderrLine: {'line': b'  308 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam3 = wp["param3"].get<float>(),\n'}
[4.657244] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.657332] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.657407] (xt_user) StderrLine: {'line': b'  309 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam4 = wp["param4"].get<float>(),\n'}
[4.657472] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.657536] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.657611] (xt_user) StderrLine: {'line': b'  310 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klat = wp["x"].get<float>(),\n'}
[4.657724] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.658585] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.658804] (xt_user) StderrLine: {'line': b'  311 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klon = wp["y"].get<float>(),\n'}
[4.658881] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.658946] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.659001] (xt_user) StderrLine: {'line': b'  312 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kalt = wp["z"].get<float>()\n'}
[4.659052] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.659095] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kmissing initializer for member \xe2\x80\x98\x1b[01m\x1b[KMissionItem::reserve\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers\x07-Wmissing-field-initializers\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.659142] (xt_user) StderrLine: {'line': b'  313 |             \x1b[01;35m\x1b[K}\x1b[m\x1b[K;\n'}
[4.659183] (xt_user) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[4.659348] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:326:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.659426] (xt_user) StderrLine: {'line': b'  326 |     for(int i = 0; \x1b[01;35m\x1b[Ki<missions.size()\x1b[m\x1b[K; i++){\n'}
[4.659474] (xt_user) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[4.716484] (-) TimerEvent: {}
[4.737130] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:60:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunsigned conversion from \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Ku_int8_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kunsigned char\x1b[m\x1b[K\xe2\x80\x99} changes value from \xe2\x80\x98\x1b[01m\x1b[K512\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[K0\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow\x07-Woverflow\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.737436] (xt_user) StderrLine: {'line': b'  406 |                     taskAnchorMissionControl.mission_num = \x1b[01;35m\x1b[K512\x1b[m\x1b[K;\n'}
[4.737730] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[4.742013] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.742218] (xt_user) StderrLine: {'line': b'  330 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K, end;\n'}
[4.742271] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.742529] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.742663] (xt_user) StderrLine: {'line': b'  330 |     double start, \x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[4.742768] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[4.742883] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kduration\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.742998] (xt_user) StderrLine: {'line': b'  331 |     double \x1b[01;35m\x1b[Kduration\x1b[m\x1b[K[10];\n'}
[4.743099] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[4.743198] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.743251] (xt_user) StderrLine: {'line': b'  332 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[4.743343] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[4.743405] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kanglevel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.743467] (xt_user) StderrLine: {'line': b'  385 |     double \x1b[01;35m\x1b[Kanglevel\x1b[m\x1b[K[2];\n'}
[4.743535] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[4.816599] (-) TimerEvent: {}
[4.917068] (-) TimerEvent: {}
[5.017594] (-) TimerEvent: {}
[5.118078] (-) TimerEvent: {}
[5.218661] (-) TimerEvent: {}
[5.319085] (-) TimerEvent: {}
[5.419526] (-) TimerEvent: {}
[5.520031] (-) TimerEvent: {}
[5.620534] (-) TimerEvent: {}
[5.721009] (-) TimerEvent: {}
[5.821616] (-) TimerEvent: {}
[5.922019] (-) TimerEvent: {}
[6.022601] (-) TimerEvent: {}
[6.123014] (-) TimerEvent: {}
[6.223969] (-) TimerEvent: {}
[6.324719] (-) TimerEvent: {}
[6.425168] (-) TimerEvent: {}
[6.525762] (-) TimerEvent: {}
[6.626157] (-) TimerEvent: {}
[6.726655] (-) TimerEvent: {}
[6.827036] (-) TimerEvent: {}
[6.927426] (-) TimerEvent: {}
[7.027824] (-) TimerEvent: {}
[7.128210] (-) TimerEvent: {}
[7.228608] (-) TimerEvent: {}
[7.329046] (-) TimerEvent: {}
[7.429599] (-) TimerEvent: {}
[7.529992] (-) TimerEvent: {}
[7.630371] (-) TimerEvent: {}
[7.730859] (-) TimerEvent: {}
[7.831328] (-) TimerEvent: {}
[7.931767] (-) TimerEvent: {}
[8.032185] (-) TimerEvent: {}
[8.132829] (-) TimerEvent: {}
[8.234170] (-) TimerEvent: {}
[8.334688] (-) TimerEvent: {}
[8.435139] (-) TimerEvent: {}
[8.535568] (-) TimerEvent: {}
[8.635946] (-) TimerEvent: {}
[8.736467] (-) TimerEvent: {}
[8.837156] (-) TimerEvent: {}
[8.937639] (-) TimerEvent: {}
[9.037968] (-) TimerEvent: {}
[9.138357] (-) TimerEvent: {}
[9.239150] (-) TimerEvent: {}
[9.340423] (-) TimerEvent: {}
[9.440887] (-) TimerEvent: {}
[9.541305] (-) TimerEvent: {}
[9.641825] (-) TimerEvent: {}
[9.742292] (-) TimerEvent: {}
[9.842820] (-) TimerEvent: {}
[9.943240] (-) TimerEvent: {}
[10.043736] (-) TimerEvent: {}
[10.144216] (-) TimerEvent: {}
[10.244615] (-) TimerEvent: {}
[10.345043] (-) TimerEvent: {}
[10.445423] (-) TimerEvent: {}
[10.545833] (-) TimerEvent: {}
[10.646233] (-) TimerEvent: {}
[10.746671] (-) TimerEvent: {}
[10.847108] (-) TimerEvent: {}
[10.947501] (-) TimerEvent: {}
[11.047878] (-) TimerEvent: {}
[11.148231] (-) TimerEvent: {}
[11.248640] (-) TimerEvent: {}
[11.349082] (-) TimerEvent: {}
[11.449442] (-) TimerEvent: {}
[11.550027] (-) TimerEvent: {}
[11.650375] (-) TimerEvent: {}
[11.751089] (-) TimerEvent: {}
[11.851480] (-) TimerEvent: {}
[11.951884] (-) TimerEvent: {}
[12.052351] (-) TimerEvent: {}
[12.152772] (-) TimerEvent: {}
[12.253236] (-) TimerEvent: {}
[12.353660] (-) TimerEvent: {}
[12.454058] (-) TimerEvent: {}
[12.554438] (-) TimerEvent: {}
[12.654850] (-) TimerEvent: {}
[12.755205] (-) TimerEvent: {}
[12.855658] (-) TimerEvent: {}
[12.956067] (-) TimerEvent: {}
[13.056476] (-) TimerEvent: {}
[13.156901] (-) TimerEvent: {}
[13.257315] (-) TimerEvent: {}
[13.357732] (-) TimerEvent: {}
[13.458248] (-) TimerEvent: {}
[13.470611] (xt_user) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable xt_user_node\x1b[0m\n'}
[13.558396] (-) TimerEvent: {}
[13.658889] (-) TimerEvent: {}
[13.759289] (-) TimerEvent: {}
[13.859854] (-) TimerEvent: {}
[13.960373] (-) TimerEvent: {}
[14.061687] (-) TimerEvent: {}
[14.162190] (-) TimerEvent: {}
[14.262635] (-) TimerEvent: {}
[14.363031] (-) TimerEvent: {}
[14.374386] (xt_user) StdoutLine: {'line': b'[100%] Built target xt_user_node\n'}
[14.383842] (xt_user) CommandEnded: {'returncode': 0}
[14.384588] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'install'}
[14.393446] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user'], 'cwd': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890/'), ('no_proxy', 'localhost,*********/8,::1'), ('LANGUAGE', 'zh_CN:en_US:en'), ('USER', 'haique'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CONDA_SHLVL', '0'), ('DESKTOP_SESSION', 'ubuntu'), ('NO_PROXY', 'localhost,*********/8,::1'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1916'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('https_proxy', 'http://127.0.0.1:7890/'), ('COLCON_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'haique'), ('http_proxy', 'http://127.0.0.1:7890/'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'haique'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/home/<USER>/miniconda3/condabin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/haique:@/tmp/.ICE-unix/1916,unix/haique:/tmp/.ICE-unix/1916'), ('PAPERSIZE', 'a4'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/ee699f72_6152_4c72_b626_1c2c2fa14a12'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.F70RA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.113'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble'), ('CONDA_PYTHON_EXE', '/home/<USER>/miniconda3/bin/python'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CONDA_EXE', '/home/<USER>/miniconda3/bin/conda'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=dc5365fa0d2bdb5d38369c01689dc449'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890/'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('COLCON', '1'), ('VTE_VERSION', '6800'), ('CMAKE_PREFIX_PATH', '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user:/opt/ros/humble')]), 'shell': False}
[14.402128] (xt_user) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[14.402568] (xt_user) StdoutLine: {'line': b'-- Execute custom install script\n'}
[14.402890] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/libplanning_hpp.so\n'}
[14.411545] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/xt_user/xt_user_node\n'}
[14.421499] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user\n'}
[14.428768] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user\n'}
[14.437460] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh\n'}
[14.447208] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv\n'}
[14.454615] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.sh\n'}
[14.462885] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv\n'}
[14.463055] (-) TimerEvent: {}
[14.471850] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.bash\n'}
[14.480264] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.sh\n'}
[14.488837] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh\n'}
[14.497973] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv\n'}
[14.506062] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv\n'}
[14.516043] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user\n'}
[14.523462] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[14.531244] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake\n'}
[14.542212] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake\n'}
[14.549704] (xt_user) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.xml\n'}
[14.559251] (xt_user) CommandEnded: {'returncode': 0}
[14.563572] (-) TimerEvent: {}
[14.580966] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 0}
[14.582182] (-) EventReactorShutdown: {}
