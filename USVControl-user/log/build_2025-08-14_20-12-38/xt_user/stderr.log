[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:177:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  177 |     kinematic_state [01;35m[Kpreposture[m[K;
      |                     [01;35m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  207 |     double [01;35m[Kstime[m[K, etime;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  207 |     double stime, [01;35m[Ketime[m[K;
      |                   [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  251 |     double [01;35m[Kstart[m[K,end;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  251 |     double start,[01;35m[Kend[m[K;
      |                  [01;35m[K^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:252:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  252 |     int [01;35m[Kdatanum[m[K = 0;
      |         [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  254 |     unsigned char [01;35m[Kperiod[m[K = 0;
      |                   [01;35m[K^~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:249:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  249 | void* MapShow([01;35m[Kvoid *arg[m[K)
      |               [01;35m[K~~~~~~^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  304 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  305 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:306:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  306 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  307 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:308:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  308 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  309 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  310 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  311 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  312 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
  313 |             [01;35m[K}[m[K;
      |             [01;35m[K^[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:326:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  326 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
      |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
  406 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
      |                                                            [01;35m[K^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  330 |     double [01;35m[Kstart[m[K, end;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  330 |     double start, [01;35m[Kend[m[K;
      |                   [01;35m[K^~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  331 |     double [01;35m[Kduration[m[K[10];
      |            [01;35m[K^~~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  332 |     int [01;35m[Kdatanum[m[K = 0;
      |         [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  385 |     double [01;35m[Kanglevel[m[K[2];
      |            [01;35m[K^~~~~~~~[m[K
