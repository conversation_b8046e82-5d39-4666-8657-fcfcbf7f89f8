[0.013s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user
[0.025s] -- Found ament_cmake: 1.3.11 (/opt/ros/humble/share/ament_cmake/cmake)
[0.132s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.141s] -- Found rclcpp: 16.0.12 (/opt/ros/humble/share/rclcpp/cmake)
[0.162s] -- Found rosidl_generator_c: 3.1.6 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.166s] -- Found rosidl_adapter: 3.1.6 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.170s] -- Found rosidl_generator_cpp: 3.1.6 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.180s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.191s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.218s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.219s] -- Found rmw_fastrtps_cpp: 6.2.7 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.306s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.354s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.446s] -- Configuring done
[0.463s] -- Generating done
[0.469s] -- Build files have been written to: /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[0.476s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake /home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user
[0.478s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.524s] [35m[1mConsolidate compiler generated dependencies of target planning_hpp[0m
[0.542s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[1.105s] [ 22%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[1.215s] [ 77%] Built target planning_hpp
[1.226s] [35m[1mConsolidate compiler generated dependencies of target xt_user_node[0m
[1.251s] [ 88%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[4.619s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[4.619s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:177:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.619s]   177 |     kinematic_state [01;35m[Kpreposture[m[K;
[4.619s]       |                     [01;35m[K^~~~~~~~~~[m[K
[4.623s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[4.623s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.623s]   207 |     double [01;35m[Kstime[m[K, etime;
[4.623s]       |            [01;35m[K^~~~~[m[K
[4.623s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.623s]   207 |     double stime, [01;35m[Ketime[m[K;
[4.624s]       |                   [01;35m[K^~~~~[m[K
[4.624s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[4.624s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.624s]   251 |     double [01;35m[Kstart[m[K,end;
[4.624s]       |            [01;35m[K^~~~~[m[K
[4.624s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.624s]   251 |     double start,[01;35m[Kend[m[K;
[4.624s]       |                  [01;35m[K^~~[m[K
[4.624s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:252:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.624s]   252 |     int [01;35m[Kdatanum[m[K = 0;
[4.624s]       |         [01;35m[K^~~~~~~[m[K
[4.624s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.625s]   254 |     unsigned char [01;35m[Kperiod[m[K = 0;
[4.625s]       |                   [01;35m[K^~~~~~[m[K
[4.625s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:249:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[4.625s]   249 | void* MapShow([01;35m[Kvoid *arg[m[K)
[4.625s]       |               [01;35m[K~~~~~~^~~[m[K
[4.641s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[4.641s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.641s]   304 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[4.641s]       |                 [01;35m[K^[m[K
[4.647s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.647s]   305 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[4.647s]       |                 [01;35m[K^[m[K
[4.651s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:306:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.651s]   306 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[4.651s]       |                 [01;35m[K^[m[K
[4.655s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.655s]   307 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[4.655s]       |                 [01;35m[K^[m[K
[4.656s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:308:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.656s]   308 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[4.656s]       |                 [01;35m[K^[m[K
[4.656s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.656s]   309 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[4.656s]       |                 [01;35m[K^[m[K
[4.656s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.656s]   310 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[4.656s]       |                 [01;35m[K^[m[K
[4.657s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.657s]   311 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[4.657s]       |                 [01;35m[K^[m[K
[4.658s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[4.658s]   312 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[4.658s]       |                 [01;35m[K^[m[K
[4.658s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[4.658s]   313 |             [01;35m[K}[m[K;
[4.658s]       |             [01;35m[K^[m[K
[4.658s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:326:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[4.658s]   326 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[4.658s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[4.736s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[4.736s]   406 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[4.736s]       |                                                            [01;35m[K^~~[m[K
[4.741s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.741s]   330 |     double [01;35m[Kstart[m[K, end;
[4.741s]       |            [01;35m[K^~~~~[m[K
[4.741s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.741s]   330 |     double start, [01;35m[Kend[m[K;
[4.741s]       |                   [01;35m[K^~~[m[K
[4.742s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.742s]   331 |     double [01;35m[Kduration[m[K[10];
[4.742s]       |            [01;35m[K^~~~~~~~[m[K
[4.742s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.742s]   332 |     int [01;35m[Kdatanum[m[K = 0;
[4.742s]       |         [01;35m[K^~~~~~~[m[K
[4.742s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.742s]   385 |     double [01;35m[Kanglevel[m[K[2];
[4.742s]       |            [01;35m[K^~~~~~~~[m[K
[13.469s] [100%] [32m[1mLinking CXX executable xt_user_node[0m
[14.373s] [100%] Built target xt_user_node
[14.383s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[14.393s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[14.401s] -- Install configuration: ""
[14.401s] -- Execute custom install script
[14.402s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[14.410s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[14.420s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[14.427s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[14.436s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[14.446s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[14.453s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[14.462s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[14.471s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[14.479s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[14.488s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[14.497s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[14.505s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv
[14.515s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[14.522s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[14.530s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[14.541s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[14.548s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.xml
[14.558s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
