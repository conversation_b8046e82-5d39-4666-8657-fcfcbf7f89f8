[0.022s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[0.131s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[0.840s] [ 22%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[1.013s] [ 77%] Built target planning_hpp
[1.062s] [ 88%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[5.521s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[5.522s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:177:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.522s]   177 |     kinematic_state [01;35m[Kpreposture[m[K;
[5.522s]       |                     [01;35m[K^~~~~~~~~~[m[K
[5.525s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[5.525s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.525s]   207 |     double [01;35m[Kstime[m[K, etime;
[5.526s]       |            [01;35m[K^~~~~[m[K
[5.526s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.526s]   207 |     double stime, [01;35m[Ketime[m[K;
[5.526s]       |                   [01;35m[K^~~~~[m[K
[5.526s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[5.526s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.526s]   251 |     double [01;35m[Kstart[m[K,end;
[5.526s]       |            [01;35m[K^~~~~[m[K
[5.526s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.526s]   251 |     double start,[01;35m[Kend[m[K;
[5.526s]       |                  [01;35m[K^~~[m[K
[5.526s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:252:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.526s]   252 |     int [01;35m[Kdatanum[m[K = 0;
[5.527s]       |         [01;35m[K^~~~~~~[m[K
[5.527s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.527s]   254 |     unsigned char [01;35m[Kperiod[m[K = 0;
[5.527s]       |                   [01;35m[K^~~~~~[m[K
[5.527s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:249:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[5.527s]   249 | void* MapShow([01;35m[Kvoid *arg[m[K)
[5.527s]       |               [01;35m[K~~~~~~^~~[m[K
[5.540s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[5.541s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.541s]   304 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[5.541s]       |                 [01;35m[K^[m[K
[5.547s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.548s]   305 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[5.548s]       |                 [01;35m[K^[m[K
[5.554s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:306:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.555s]   306 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[5.555s]       |                 [01;35m[K^[m[K
[5.561s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.561s]   307 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[5.562s]       |                 [01;35m[K^[m[K
[5.562s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:308:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.562s]   308 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[5.562s]       |                 [01;35m[K^[m[K
[5.562s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.562s]   309 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[5.562s]       |                 [01;35m[K^[m[K
[5.562s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.562s]   310 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[5.562s]       |                 [01;35m[K^[m[K
[5.565s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.565s]   311 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[5.565s]       |                 [01;35m[K^[m[K
[5.565s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.565s]   312 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[5.565s]       |                 [01;35m[K^[m[K
[5.565s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[5.565s]   313 |             [01;35m[K}[m[K;
[5.565s]       |             [01;35m[K^[m[K
[5.566s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:326:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.566s]   326 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[5.566s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[5.693s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[5.693s]   406 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[5.694s]       |                                                            [01;35m[K^~~[m[K
[5.699s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.701s]   330 |     double [01;35m[Kstart[m[K, end;
[5.701s]       |            [01;35m[K^~~~~[m[K
[5.701s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.702s]   330 |     double start, [01;35m[Kend[m[K;
[5.702s]       |                   [01;35m[K^~~[m[K
[5.702s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.703s]   331 |     double [01;35m[Kduration[m[K[10];
[5.703s]       |            [01;35m[K^~~~~~~~[m[K
[5.703s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.704s]   332 |     int [01;35m[Kdatanum[m[K = 0;
[5.704s]       |         [01;35m[K^~~~~~~[m[K
[5.704s] [01m[K/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.704s]   385 |     double [01;35m[Kanglevel[m[K[2];
[5.704s]       |            [01;35m[K^~~~~~~~[m[K
[17.457s] [100%] [32m[1mLinking CXX executable xt_user_node[0m
[18.474s] [100%] Built target xt_user_node
[18.487s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[18.495s] Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[18.505s] -- Install configuration: ""
[18.505s] -- Execute custom install script
[18.505s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[18.505s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[18.506s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[18.506s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[18.506s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[18.508s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[18.508s] -- Symlinking: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv
[18.520s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[18.520s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[18.520s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[18.520s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[18.520s] -- Up-to-date symlink: /home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.xml
[18.523s] Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
