[0.123s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.123s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x79bb96cd87c0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x79bb96cd8250>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x79bb96cd8250>>)
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.190s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.190s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/haique_work/usv_control/project2/USVControl-user'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.194s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.195s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.196s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.200s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.200s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.201s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'ros'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['cmake', 'python']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'cmake'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extensions ['python_setup_py']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143544) by extension 'python_setup_py'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'ros'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['cmake', 'python']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'cmake'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extensions ['python_setup_py']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_143757) by extension 'python_setup_py'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'ros'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['cmake', 'python']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'cmake'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extensions ['python_setup_py']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144041) by extension 'python_setup_py'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'ros'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['cmake', 'python']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'cmake'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extensions ['python_setup_py']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_144719) by extension 'python_setup_py'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['colcon_meta']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'colcon_meta'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['ros']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'ros'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['cmake', 'python']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'cmake'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extensions ['python_setup_py']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145235) by extension 'python_setup_py'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ignore', 'ignore_ament_install']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ignore_ament_install'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_pkg']
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_pkg'
[0.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'ros'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['cmake', 'python']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'cmake'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extensions ['python_setup_py']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_145410) by extension 'python_setup_py'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ignore', 'ignore_ament_install']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ignore_ament_install'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_pkg']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_pkg'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['colcon_meta']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'colcon_meta'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['ros']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'ros'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['cmake', 'python']
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'cmake'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python'
[0.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extensions ['python_setup_py']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_151536) by extension 'python_setup_py'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_pkg'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['colcon_meta']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'colcon_meta'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['ros']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'ros'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['cmake', 'python']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'cmake'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extensions ['python_setup_py']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153656) by extension 'python_setup_py'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ignore', 'ignore_ament_install']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ignore_ament_install'
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_pkg']
[0.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_pkg'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['colcon_meta']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'colcon_meta'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['ros']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'ros'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['cmake', 'python']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'cmake'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extensions ['python_setup_py']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_153837) by extension 'python_setup_py'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['ignore', 'ignore_ament_install']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ignore'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ignore_ament_install'
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['colcon_pkg']
[0.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'ros'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['cmake', 'python']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'cmake'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'python'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extensions ['python_setup_py']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202043) by extension 'python_setup_py'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['ignore', 'ignore_ament_install']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ignore'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ignore_ament_install'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['colcon_pkg']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'colcon_pkg'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['colcon_meta']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'colcon_meta'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['ros']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'ros'
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['cmake', 'python']
[0.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'cmake'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'python'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extensions ['python_setup_py']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202237) by extension 'python_setup_py'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['ignore', 'ignore_ament_install']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ignore'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ignore_ament_install'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['colcon_pkg']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'colcon_pkg'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['colcon_meta']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'colcon_meta'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['ros']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'ros'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['cmake', 'python']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'cmake'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'python'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extensions ['python_setup_py']
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202507) by extension 'python_setup_py'
[0.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['colcon_meta']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'colcon_meta'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['ros']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'ros'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['cmake', 'python']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'cmake'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'python'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extensions ['python_setup_py']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202647) by extension 'python_setup_py'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['ignore', 'ignore_ament_install']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'ignore'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'ignore_ament_install'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['colcon_pkg']
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'colcon_pkg'
[0.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'ros'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['cmake', 'python']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'cmake'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'python'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extensions ['python_setup_py']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202804) by extension 'python_setup_py'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['ignore', 'ignore_ament_install']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'ignore'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'ignore_ament_install'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['colcon_pkg']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'colcon_pkg'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['colcon_meta']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'colcon_meta'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['ros']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'ros'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['cmake', 'python']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'cmake'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'python'
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extensions ['python_setup_py']
[0.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_202919) by extension 'python_setup_py'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'ignore_ament_install'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extensions ['colcon_pkg']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'colcon_pkg'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extensions ['colcon_meta']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'colcon_meta'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extensions ['ros']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'ros'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extensions ['cmake', 'python']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'cmake'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'python'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extensions ['python_setup_py']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203811) by extension 'python_setup_py'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extensions ['ignore', 'ignore_ament_install']
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'ignore'
[0.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extensions ['ros']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'ros'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extensions ['cmake', 'python']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'cmake'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'python'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extensions ['python_setup_py']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_203922) by extension 'python_setup_py'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extensions ['ignore', 'ignore_ament_install']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'ignore'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'ignore_ament_install'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extensions ['colcon_pkg']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'colcon_pkg'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extensions ['colcon_meta']
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'colcon_meta'
[0.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'cmake'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'python'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extensions ['python_setup_py']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204052) by extension 'python_setup_py'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extensions ['ignore', 'ignore_ament_install']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'ignore'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'ignore_ament_install'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extensions ['colcon_pkg']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'colcon_pkg'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extensions ['colcon_meta']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'colcon_meta'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extensions ['ros']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'ros'
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extensions ['cmake', 'python']
[0.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204203) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extensions ['ignore', 'ignore_ament_install']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'ignore'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'ignore_ament_install'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extensions ['colcon_pkg']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'colcon_pkg'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extensions ['colcon_meta']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'colcon_meta'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extensions ['ros']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'ros'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extensions ['cmake', 'python']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'cmake'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'python'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extensions ['python_setup_py']
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204402) by extension 'python_setup_py'
[0.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'colcon_meta'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extensions ['ros']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'ros'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extensions ['cmake', 'python']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'cmake'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'python'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extensions ['python_setup_py']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204519) by extension 'python_setup_py'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extensions ['ignore', 'ignore_ament_install']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'ignore'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'ignore_ament_install'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extensions ['colcon_pkg']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'colcon_pkg'
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extensions ['colcon_meta']
[0.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'ros'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extensions ['cmake', 'python']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'cmake'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'python'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extensions ['python_setup_py']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204633) by extension 'python_setup_py'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extensions ['ignore', 'ignore_ament_install']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'ignore'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'ignore_ament_install'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extensions ['colcon_pkg']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'colcon_pkg'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extensions ['colcon_meta']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'colcon_meta'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extensions ['ros']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'ros'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extensions ['cmake', 'python']
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'cmake'
[0.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extensions ['python_setup_py']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204822) by extension 'python_setup_py'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extensions ['ignore', 'ignore_ament_install']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'ignore'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'ignore_ament_install'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extensions ['colcon_pkg']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'colcon_pkg'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extensions ['colcon_meta']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'colcon_meta'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extensions ['ros']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'ros'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extensions ['cmake', 'python']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'cmake'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'python'
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extensions ['python_setup_py']
[0.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250814_204946) by extension 'python_setup_py'
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.220s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.221s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.221s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.244s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.246s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/haique_work/usv_control/project2/USVControl-user/install
[0.249s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 273 installed packages in /opt/ros/humble
[0.250s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.291s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.291s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.291s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.293s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.293s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.293s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/ControlNode/planning'
[0.296s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.296s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.296s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.307s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[2.392s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --build /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user -- -j8 -l8
[2.407s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[2.441s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[2.443s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble CONDA_PROMPT_MODIFIER=(base) /usr/bin/cmake --install /home/<USER>/haique_work/usv_control/project2/USVControl-user/build/xt_user
[2.447s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[2.448s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[2.448s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[2.448s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[2.449s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[2.449s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[2.450s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[2.450s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[2.450s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[2.451s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[2.451s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[2.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[2.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[2.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.453s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[2.455s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[2.456s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[2.456s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[2.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[2.458s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[2.459s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[2.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake module files
[2.459s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user' for CMake config files
[2.460s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[2.460s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[2.460s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[2.461s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[2.461s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib'
[2.461s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[2.463s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[2.463s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[2.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[2.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[2.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[2.464s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/bin'
[2.465s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[2.465s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[2.466s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.sh'
[2.467s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.bash'
[2.468s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[2.468s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[2.469s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.469s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.469s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[2.469s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.475s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.475s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.475s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.486s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.486s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.ps1'
[2.487s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_ps1.py'
[2.488s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.ps1'
[2.489s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.sh'
[2.490s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/_local_setup_util_sh.py'
[2.490s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.sh'
[2.491s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.bash'
[2.491s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.bash'
[2.492s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/local_setup.zsh'
[2.494s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/USVControl-user/install/setup.zsh'
