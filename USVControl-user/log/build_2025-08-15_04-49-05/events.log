[0.000000] (-) TimerEvent: {}
[0.003359] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.003958] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.017185] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.018083] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[0.018995] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/333/project2_0815'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1569'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1711'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '18393'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:22143'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1711,unix/xumj-virtual-machine:/tmp/.ICE-unix/1711'), ('INVOCATION_ID', '3d9646cf809448b2ab7517905cc62e8f'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.2XD9A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1d51052266.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.101777] (-) TimerEvent: {}
[0.112184] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target planning_hpp\x1b[0m\n'}
[0.141509] (xt_user) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[0.202067] (-) TimerEvent: {}
[0.302971] (-) TimerEvent: {}
[0.404748] (-) TimerEvent: {}
[0.506000] (-) TimerEvent: {}
[0.606670] (-) TimerEvent: {}
[0.708901] (-) TimerEvent: {}
[0.738501] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::CalculateTrackAccuracy()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.738808] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:266:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kpaired_actual_points\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.738907] (xt_user) StderrLine: {'line': b'  266 |         float \x1b[01;35m\x1b[Kpaired_actual_points\x1b[m\x1b[K[10][2] = {0}; // \xe7\x94\xa8\xe4\xba\x8e\xe5\xad\x98\xe5\x82\xa8\xe9\x85\x8d\xe5\xaf\xb9\xe5\xa5\xbd\xe7\x9a\x84\xe5\xae\x9e\xe9\x99\x85\xe7\x82\xb9\xef\xbc\x8c\xe6\x96\xb9\xe4\xbe\xbf\xe6\x89\x93\xe5\x8d\xb0\n'}
[0.739914] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.741323] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.741527] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Knowpos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.741601] (xt_user) StderrLine: {'line': b'  347 |         double \x1b[01;35m\x1b[Knowpos\x1b[m\x1b[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};\n'}
[0.741684] (xt_user) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[0.741749] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:33:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Khomepos\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.741824] (xt_user) StderrLine: {'line': b'  347 |         double nowpos[2] = {0}, \x1b[01;35m\x1b[Khomepos\x1b[m\x1b[K[2] = {0}, lengthangle[2] = {0};\n'}
[0.741886] (xt_user) StderrLine: {'line': b'      |                                 \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.742024] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:51:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Klengthangle\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.742114] (xt_user) StderrLine: {'line': b'  347 |         double nowpos[2] = {0}, homepos[2] = {0}, \x1b[01;35m\x1b[Klengthangle\x1b[m\x1b[K[2] = {0};\n'}
[0.742175] (xt_user) StderrLine: {'line': b'      |                                                   \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.744176] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState, std::vector<std::vector<int> >*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.744371] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:601:23:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kpretovel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.744438] (xt_user) StderrLine: {'line': b'  601 |                 float \x1b[01;35m\x1b[Kpretovel\x1b[m\x1b[K[2] = {0};\n'}
[0.744498] (xt_user) StderrLine: {'line': b'      |                       \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[0.745220] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:474:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Klengthangle\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.745425] (xt_user) StderrLine: {'line': b'  474 |         float \x1b[01;35m\x1b[Klengthangle\x1b[m\x1b[K[2] = {0}, vel = 0;    // \xe9\x95\xbf\xe5\xba\xa6\xe8\xa7\x92\xe5\xba\xa6\xe5\x92\x8c\xe9\x80\x9f\xe5\xba\xa6\xe5\x8f\x98\xe9\x87\x8f\n'}
[0.745495] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.809579] (-) TimerEvent: {}
[0.910645] (-) TimerEvent: {}
[1.011510] (-) TimerEvent: {}
[1.013005] (xt_user) StdoutLine: {'line': b'[ 22%] \x1b[32m\x1b[1mLinking CXX shared library libplanning_hpp.so\x1b[0m\n'}
[1.113323] (-) TimerEvent: {}
[1.213840] (-) TimerEvent: {}
[1.269465] (xt_user) StdoutLine: {'line': b'[ 77%] Built target planning_hpp\n'}
[1.314246] (-) TimerEvent: {}
[1.314781] (xt_user) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o\x1b[0m\n'}
[1.418415] (-) TimerEvent: {}
[1.519167] (-) TimerEvent: {}
[1.620167] (-) TimerEvent: {}
[1.721972] (-) TimerEvent: {}
[1.822574] (-) TimerEvent: {}
[1.924432] (-) TimerEvent: {}
[2.025182] (-) TimerEvent: {}
[2.126620] (-) TimerEvent: {}
[2.227699] (-) TimerEvent: {}
[2.330094] (-) TimerEvent: {}
[2.431040] (-) TimerEvent: {}
[2.532811] (-) TimerEvent: {}
[2.634355] (-) TimerEvent: {}
[2.735678] (-) TimerEvent: {}
[2.836741] (-) TimerEvent: {}
[2.938637] (-) TimerEvent: {}
[3.040105] (-) TimerEvent: {}
[3.142705] (-) TimerEvent: {}
[3.244850] (-) TimerEvent: {}
[3.346988] (-) TimerEvent: {}
[3.447410] (-) TimerEvent: {}
[3.549670] (-) TimerEvent: {}
[3.650140] (-) TimerEvent: {}
[3.751016] (-) TimerEvent: {}
[3.852506] (-) TimerEvent: {}
[3.953202] (-) TimerEvent: {}
[4.054476] (-) TimerEvent: {}
[4.156094] (-) TimerEvent: {}
[4.258892] (-) TimerEvent: {}
[4.359987] (-) TimerEvent: {}
[4.464401] (-) TimerEvent: {}
[4.564871] (-) TimerEvent: {}
[4.665254] (-) TimerEvent: {}
[4.765796] (-) TimerEvent: {}
[4.866733] (-) TimerEvent: {}
[4.969228] (-) TimerEvent: {}
[5.072006] (-) TimerEvent: {}
[5.172435] (-) TimerEvent: {}
[5.272841] (-) TimerEvent: {}
[5.373419] (-) TimerEvent: {}
[5.475204] (-) TimerEvent: {}
[5.576256] (-) TimerEvent: {}
[5.677166] (-) TimerEvent: {}
[5.777637] (-) TimerEvent: {}
[5.880150] (-) TimerEvent: {}
[5.981088] (-) TimerEvent: {}
[6.083162] (-) TimerEvent: {}
[6.185909] (-) TimerEvent: {}
[6.287645] (-) TimerEvent: {}
[6.388278] (-) TimerEvent: {}
[6.489535] (-) TimerEvent: {}
[6.593546] (-) TimerEvent: {}
[6.695317] (-) TimerEvent: {}
[6.795983] (-) TimerEvent: {}
[6.897606] (-) TimerEvent: {}
[7.003910] (-) TimerEvent: {}
[7.109874] (-) TimerEvent: {}
[7.210255] (-) TimerEvent: {}
[7.310803] (-) TimerEvent: {}
[7.412244] (-) TimerEvent: {}
[7.513187] (-) TimerEvent: {}
[7.527214] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.527470] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:177:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kpreposture\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.527550] (xt_user) StderrLine: {'line': b'  177 |     kinematic_state \x1b[01;35m\x1b[Kpreposture\x1b[m\x1b[K;\n'}
[7.527648] (xt_user) StderrLine: {'line': b'      |                     \x1b[01;35m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[7.534869] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.535990] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.537317] (xt_user) StderrLine: {'line': b'  207 |     double \x1b[01;35m\x1b[Kstime\x1b[m\x1b[K, etime;\n'}
[7.537393] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[7.537454] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ketime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.537515] (xt_user) StderrLine: {'line': b'  207 |     double stime, \x1b[01;35m\x1b[Ketime\x1b[m\x1b[K;\n'}
[7.537573] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[7.537628] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid* MapShow(void*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.537700] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.537758] (xt_user) StderrLine: {'line': b'  251 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K,end;\n'}
[7.537814] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[7.537868] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:18:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.537925] (xt_user) StderrLine: {'line': b'  251 |     double start,\x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[7.537980] (xt_user) StderrLine: {'line': b'      |                  \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[7.538035] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:252:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.538097] (xt_user) StderrLine: {'line': b'  252 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[7.538152] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[7.538208] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kperiod\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.538266] (xt_user) StderrLine: {'line': b'  254 |     unsigned char \x1b[01;35m\x1b[Kperiod\x1b[m\x1b[K = 0;\n'}
[7.538322] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[7.538377] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:249:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Karg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.538439] (xt_user) StderrLine: {'line': b'  249 | void* MapShow(\x1b[01;35m\x1b[Kvoid *arg\x1b[m\x1b[K)\n'}
[7.538496] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K~~~~~~^~~\x1b[m\x1b[K\n'}
[7.567778] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[7.568677] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.568970] (xt_user) StderrLine: {'line': b'  304 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kcommand = wp["command"].get<uint16_t>(),\n'}
[7.569063] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.582568] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.582834] (xt_user) StderrLine: {'line': b'  305 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kseq = wp["seq"].get<uint8_t>(),\n'}
[7.582948] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.596877] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:306:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.597520] (xt_user) StderrLine: {'line': b'  306 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam1 = wp["param1"].get<float>(),\n'}
[7.597684] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.607703] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.608096] (xt_user) StderrLine: {'line': b'  307 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam2 = wp["param2"].get<float>(),\n'}
[7.608213] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.608539] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:308:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.608660] (xt_user) StderrLine: {'line': b'  308 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam3 = wp["param3"].get<float>(),\n'}
[7.608737] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.609295] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.609423] (xt_user) StderrLine: {'line': b'  309 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam4 = wp["param4"].get<float>(),\n'}
[7.609492] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.609570] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.609640] (xt_user) StderrLine: {'line': b'  310 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klat = wp["x"].get<float>(),\n'}
[7.609706] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.611196] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.611336] (xt_user) StderrLine: {'line': b'  311 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klon = wp["y"].get<float>(),\n'}
[7.611464] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.611635] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.611740] (xt_user) StderrLine: {'line': b'  312 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kalt = wp["z"].get<float>()\n'}
[7.614573] (-) TimerEvent: {}
[7.615905] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.616066] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kmissing initializer for member \xe2\x80\x98\x1b[01m\x1b[KMissionItem::reserve\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers\x07-Wmissing-field-initializers\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.616156] (xt_user) StderrLine: {'line': b'  313 |             \x1b[01;35m\x1b[K}\x1b[m\x1b[K;\n'}
[7.616215] (xt_user) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[7.616351] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:326:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.616423] (xt_user) StderrLine: {'line': b'  326 |     for(int i = 0; \x1b[01;35m\x1b[Ki<missions.size()\x1b[m\x1b[K; i++){\n'}
[7.616482] (xt_user) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[7.717123] (-) TimerEvent: {}
[7.790271] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:60:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunsigned conversion from \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Ku_int8_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kunsigned char\x1b[m\x1b[K\xe2\x80\x99} changes value from \xe2\x80\x98\x1b[01m\x1b[K512\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[K0\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow\x07-Woverflow\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.790768] (xt_user) StderrLine: {'line': b'  406 |                     taskAnchorMissionControl.mission_num = \x1b[01;35m\x1b[K512\x1b[m\x1b[K;\n'}
[7.790866] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[7.800035] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.801109] (xt_user) StderrLine: {'line': b'  330 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K, end;\n'}
[7.801440] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[7.801533] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.801603] (xt_user) StderrLine: {'line': b'  330 |     double start, \x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[7.801672] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[7.801731] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kduration\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.801796] (xt_user) StderrLine: {'line': b'  331 |     double \x1b[01;35m\x1b[Kduration\x1b[m\x1b[K[10];\n'}
[7.801855] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[7.801912] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.801987] (xt_user) StderrLine: {'line': b'  332 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[7.802048] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[7.802105] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kanglevel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[7.802172] (xt_user) StderrLine: {'line': b'  385 |     double \x1b[01;35m\x1b[Kanglevel\x1b[m\x1b[K[2];\n'}
[7.802230] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[7.817375] (-) TimerEvent: {}
[7.921046] (-) TimerEvent: {}
[8.021610] (-) TimerEvent: {}
[8.122784] (-) TimerEvent: {}
[8.225773] (-) TimerEvent: {}
[8.330132] (-) TimerEvent: {}
[8.430659] (-) TimerEvent: {}
[8.532031] (-) TimerEvent: {}
[8.634994] (-) TimerEvent: {}
[8.735897] (-) TimerEvent: {}
[8.836532] (-) TimerEvent: {}
[8.940239] (-) TimerEvent: {}
[9.042248] (-) TimerEvent: {}
[9.144128] (-) TimerEvent: {}
[9.247823] (-) TimerEvent: {}
[9.349454] (-) TimerEvent: {}
[9.451536] (-) TimerEvent: {}
[9.552437] (-) TimerEvent: {}
[9.653316] (-) TimerEvent: {}
[9.757938] (-) TimerEvent: {}
[9.858355] (-) TimerEvent: {}
[9.960186] (-) TimerEvent: {}
[10.060653] (-) TimerEvent: {}
[10.163604] (-) TimerEvent: {}
[10.271131] (-) TimerEvent: {}
[10.372796] (-) TimerEvent: {}
[10.473442] (-) TimerEvent: {}
[10.574930] (-) TimerEvent: {}
[10.675316] (-) TimerEvent: {}
[10.776633] (-) TimerEvent: {}
[10.879892] (-) TimerEvent: {}
[10.984221] (-) TimerEvent: {}
[11.086165] (-) TimerEvent: {}
[11.195666] (-) TimerEvent: {}
[11.296492] (-) TimerEvent: {}
[11.398291] (-) TimerEvent: {}
[11.500397] (-) TimerEvent: {}
[11.601209] (-) TimerEvent: {}
[11.703061] (-) TimerEvent: {}
[11.806339] (-) TimerEvent: {}
[11.906965] (-) TimerEvent: {}
[12.009850] (-) TimerEvent: {}
[12.110666] (-) TimerEvent: {}
[12.211249] (-) TimerEvent: {}
[12.313294] (-) TimerEvent: {}
[12.415737] (-) TimerEvent: {}
[12.516897] (-) TimerEvent: {}
[12.618677] (-) TimerEvent: {}
[12.719670] (-) TimerEvent: {}
[12.820677] (-) TimerEvent: {}
[12.922229] (-) TimerEvent: {}
[13.023952] (-) TimerEvent: {}
[13.125286] (-) TimerEvent: {}
[13.226849] (-) TimerEvent: {}
[13.327386] (-) TimerEvent: {}
[13.428223] (-) TimerEvent: {}
[13.532108] (-) TimerEvent: {}
[13.633544] (-) TimerEvent: {}
[13.739401] (-) TimerEvent: {}
[13.840504] (-) TimerEvent: {}
[13.941357] (-) TimerEvent: {}
[14.042134] (-) TimerEvent: {}
[14.146326] (-) TimerEvent: {}
[14.248688] (-) TimerEvent: {}
[14.349675] (-) TimerEvent: {}
[14.450175] (-) TimerEvent: {}
[14.550833] (-) TimerEvent: {}
[14.652038] (-) TimerEvent: {}
[14.755390] (-) TimerEvent: {}
[14.858488] (-) TimerEvent: {}
[14.959489] (-) TimerEvent: {}
[15.063422] (-) TimerEvent: {}
[15.163997] (-) TimerEvent: {}
[15.265608] (-) TimerEvent: {}
[15.371717] (-) TimerEvent: {}
[15.474044] (-) TimerEvent: {}
[15.575832] (-) TimerEvent: {}
[15.677063] (-) TimerEvent: {}
[15.777802] (-) TimerEvent: {}
[15.878309] (-) TimerEvent: {}
[15.980347] (-) TimerEvent: {}
[16.080861] (-) TimerEvent: {}
[16.184563] (-) TimerEvent: {}
[16.287051] (-) TimerEvent: {}
[16.388537] (-) TimerEvent: {}
[16.489065] (-) TimerEvent: {}
[16.592912] (-) TimerEvent: {}
[16.694403] (-) TimerEvent: {}
[16.796366] (-) TimerEvent: {}
[16.896885] (-) TimerEvent: {}
[16.999094] (-) TimerEvent: {}
[17.100214] (-) TimerEvent: {}
[17.201427] (-) TimerEvent: {}
[17.302238] (-) TimerEvent: {}
[17.402843] (-) TimerEvent: {}
[17.503392] (-) TimerEvent: {}
[17.605428] (-) TimerEvent: {}
[17.709654] (-) TimerEvent: {}
[17.812174] (-) TimerEvent: {}
[17.913196] (-) TimerEvent: {}
[18.013745] (-) TimerEvent: {}
[18.117299] (-) TimerEvent: {}
[18.219902] (-) TimerEvent: {}
[18.322552] (-) TimerEvent: {}
[18.424806] (-) TimerEvent: {}
[18.526372] (-) TimerEvent: {}
[18.627269] (-) TimerEvent: {}
[18.727994] (-) TimerEvent: {}
[18.829270] (-) TimerEvent: {}
[18.930773] (-) TimerEvent: {}
[19.033482] (-) TimerEvent: {}
[19.134619] (-) TimerEvent: {}
[19.235200] (-) TimerEvent: {}
[19.335940] (-) TimerEvent: {}
[19.436367] (-) TimerEvent: {}
[19.537111] (-) TimerEvent: {}
[19.638051] (-) TimerEvent: {}
[19.739220] (-) TimerEvent: {}
[19.843799] (-) TimerEvent: {}
[19.945296] (-) TimerEvent: {}
[20.046011] (-) TimerEvent: {}
[20.147072] (-) TimerEvent: {}
[20.247906] (-) TimerEvent: {}
[20.349194] (-) TimerEvent: {}
[20.452344] (-) TimerEvent: {}
[20.556182] (-) TimerEvent: {}
[20.657432] (-) TimerEvent: {}
[20.762555] (-) TimerEvent: {}
[20.862957] (-) TimerEvent: {}
[20.963450] (-) TimerEvent: {}
[21.064093] (-) TimerEvent: {}
[21.166453] (-) TimerEvent: {}
[21.267578] (-) TimerEvent: {}
[21.370266] (-) TimerEvent: {}
[21.471530] (-) TimerEvent: {}
[21.576108] (-) TimerEvent: {}
[21.679544] (-) TimerEvent: {}
[21.780701] (-) TimerEvent: {}
[21.883401] (-) TimerEvent: {}
[21.984597] (-) TimerEvent: {}
[22.086801] (-) TimerEvent: {}
[22.187454] (-) TimerEvent: {}
[22.288023] (-) TimerEvent: {}
[22.390315] (-) TimerEvent: {}
[22.490680] (-) TimerEvent: {}
[22.592083] (-) TimerEvent: {}
[22.693306] (-) TimerEvent: {}
[22.794140] (-) TimerEvent: {}
[22.894659] (-) TimerEvent: {}
[22.998203] (-) TimerEvent: {}
[23.098547] (-) TimerEvent: {}
[23.203532] (-) TimerEvent: {}
[23.304056] (-) TimerEvent: {}
[23.405750] (-) TimerEvent: {}
[23.506626] (-) TimerEvent: {}
[23.609545] (-) TimerEvent: {}
[23.711587] (-) TimerEvent: {}
[23.813193] (-) TimerEvent: {}
[23.914185] (-) TimerEvent: {}
[24.016259] (-) TimerEvent: {}
[24.117433] (-) TimerEvent: {}
[24.218145] (-) TimerEvent: {}
[24.322025] (-) TimerEvent: {}
[24.352606] (xt_user) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable xt_user_node\x1b[0m\n'}
[24.425251] (-) TimerEvent: {}
[24.525896] (-) TimerEvent: {}
[24.627386] (-) TimerEvent: {}
[24.728883] (-) TimerEvent: {}
[24.833163] (-) TimerEvent: {}
[24.934536] (-) TimerEvent: {}
[25.035489] (-) TimerEvent: {}
[25.142123] (-) TimerEvent: {}
[25.246023] (-) TimerEvent: {}
[25.347084] (-) TimerEvent: {}
[25.448588] (-) TimerEvent: {}
[25.549887] (-) TimerEvent: {}
[25.651962] (-) TimerEvent: {}
[25.753142] (-) TimerEvent: {}
[25.859560] (-) TimerEvent: {}
[25.961516] (-) TimerEvent: {}
[26.062489] (-) TimerEvent: {}
[26.163777] (-) TimerEvent: {}
[26.208481] (xt_user) StdoutLine: {'line': b'[100%] Built target xt_user_node\n'}
[26.232930] (xt_user) CommandEnded: {'returncode': 0}
[26.233951] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'install'}
[26.249548] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user'], 'cwd': '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/333/project2_0815'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1569'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1711'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '18393'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:22143'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1711,unix/xumj-virtual-machine:/tmp/.ICE-unix/1711'), ('INVOCATION_ID', '3d9646cf809448b2ab7517905cc62e8f'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.2XD9A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-1d51052266.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=6d1157e87f051a443b9cbb83689e391d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[26.263823] (xt_user) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[26.264343] (-) TimerEvent: {}
[26.264724] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/libplanning_hpp.so\n'}
[26.265561] (xt_user) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""\n'}
[26.265657] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/xt_user/xt_user_node\n'}
[26.282737] (xt_user) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""\n'}
[26.290153] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user\n'}
[26.290391] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user\n'}
[26.290456] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv\n'}
[26.290517] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv\n'}
[26.290573] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.bash\n'}
[26.290645] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.sh\n'}
[26.290701] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh\n'}
[26.290758] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv\n'}
[26.290817] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.dsv\n'}
[26.290875] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user\n'}
[26.290932] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[26.290990] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake\n'}
[26.291049] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake\n'}
[26.291105] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.xml\n'}
[26.293966] (xt_user) CommandEnded: {'returncode': 0}
[26.334743] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 0}
[26.338123] (-) EventReactorShutdown: {}
