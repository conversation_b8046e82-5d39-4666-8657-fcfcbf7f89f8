[0.189s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'xt_user']
[0.189s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['xt_user'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x72c219e33280>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x72c219f78790>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x72c219f78790>>)
[0.415s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.416s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.416s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.416s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.416s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.416s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.416s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/333/project2_0815/USVControl-user'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.438s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['ignore', 'ignore_ament_install']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'ignore'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'ignore_ament_install'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['colcon_pkg']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'colcon_pkg'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['colcon_meta']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'colcon_meta'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['ros']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'ros'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['cmake', 'python']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'cmake'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'python'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extensions ['python_setup_py']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(tiaoshilog) by extension 'python_setup_py'
[0.443s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.443s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.443s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.443s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.443s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.470s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.470s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.473s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 271 installed packages in /opt/ros/humble
[0.477s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.539s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.539s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.539s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.539s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.540s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.540s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.540s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.540s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.540s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.540s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.540s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.544s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.545s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.545s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning'
[0.548s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.548s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.548s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.567s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[26.774s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[26.791s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
[26.834s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[26.835s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
[26.838s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user' for CMake module files
[26.839s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user' for CMake config files
[26.839s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[26.839s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[26.842s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[26.842s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[26.844s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib'
[26.844s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[26.845s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[26.846s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[26.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[26.847s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/bin'
[26.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[26.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[26.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/bin'
[26.850s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[26.851s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[26.852s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.sh'
[26.852s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.bash'
[26.854s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[26.855s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[26.856s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[26.856s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user' for CMake module files
[26.857s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user' for CMake config files
[26.861s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[26.863s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[26.864s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[26.864s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[26.865s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib'
[26.865s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[26.865s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[26.866s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[26.867s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[26.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/bin'
[26.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[26.867s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[26.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/bin'
[26.868s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[26.872s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[26.872s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.sh'
[26.873s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.bash'
[26.874s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[26.874s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[26.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[26.875s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[26.878s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[26.879s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[26.885s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[26.885s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[26.885s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[26.926s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[26.927s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.ps1'
[26.929s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/333/project2_0815/USVControl-user/install/_local_setup_util_ps1.py'
[26.931s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.ps1'
[26.932s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.sh'
[26.933s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/333/project2_0815/USVControl-user/install/_local_setup_util_sh.py'
[26.934s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.sh'
[26.937s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.bash'
[26.938s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.bash'
[26.941s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/333/project2_0815/USVControl-user/install/local_setup.zsh'
[26.942s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/333/project2_0815/USVControl-user/install/setup.zsh'
