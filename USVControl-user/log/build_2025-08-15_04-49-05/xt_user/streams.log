[0.021s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[0.108s] [35m[1mConsolidate compiler generated dependencies of target planning_hpp[0m
[0.137s] [ 11%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[0.734s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::CalculateTrackAccuracy()[m[K’:
[0.735s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:266:15:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpaired_actual_points[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[0.736s]   266 |         float [01;35m[Kpaired_actual_points[m[K[10][2] = {0}; // 用于存储配对好的实际点，方便打印
[0.736s]       |               [01;35m[K^~~~~~~~~~~~~~~~~~~~[m[K
[0.737s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[0.737s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:16:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Knowpos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[0.737s]   347 |         double [01;35m[Knowpos[m[K[2] = {0}, homepos[2] = {0}, lengthangle[2] = {0};
[0.737s]       |                [01;35m[K^~~~~~[m[K
[0.737s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:33:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Khomepos[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[0.738s]   347 |         double nowpos[2] = {0}, [01;35m[Khomepos[m[K[2] = {0}, lengthangle[2] = {0};
[0.738s]       |                                 [01;35m[K^~~~~~~[m[K
[0.738s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:347:51:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[0.738s]   347 |         double nowpos[2] = {0}, homepos[2] = {0}, [01;35m[Klengthangle[m[K[2] = {0};
[0.738s]       |                                                   [01;35m[K^~~~~~~~~~~[m[K
[0.740s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState, std::vector<std::vector<int> >*)[m[K’:
[0.740s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:601:23:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kpretovel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[0.740s]   601 |                 float [01;35m[Kpretovel[m[K[2] = {0};
[0.740s]       |                       [01;35m[K^~~~~~~~[m[K
[0.741s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:474:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Klengthangle[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[0.741s]   474 |         float [01;35m[Klengthangle[m[K[2] = {0}, vel = 0;    // 长度角度和速度变量
[0.741s]       |               [01;35m[K^~~~~~~~~~~[m[K
[1.009s] [ 22%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[1.265s] [ 77%] Built target planning_hpp
[1.311s] [ 88%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[7.523s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[7.523s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:177:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[7.523s]   177 |     kinematic_state [01;35m[Kpreposture[m[K;
[7.523s]       |                     [01;35m[K^~~~~~~~~~[m[K
[7.531s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[7.533s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[7.533s]   207 |     double [01;35m[Kstime[m[K, etime;
[7.533s]       |            [01;35m[K^~~~~[m[K
[7.533s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:207:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[7.533s]   207 |     double stime, [01;35m[Ketime[m[K;
[7.533s]       |                   [01;35m[K^~~~~[m[K
[7.533s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[7.533s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[7.533s]   251 |     double [01;35m[Kstart[m[K,end;
[7.534s]       |            [01;35m[K^~~~~[m[K
[7.534s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:251:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.534s]   251 |     double start,[01;35m[Kend[m[K;
[7.534s]       |                  [01;35m[K^~~[m[K
[7.534s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:252:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.534s]   252 |     int [01;35m[Kdatanum[m[K = 0;
[7.534s]       |         [01;35m[K^~~~~~~[m[K
[7.534s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:254:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.534s]   254 |     unsigned char [01;35m[Kperiod[m[K = 0;
[7.534s]       |                   [01;35m[K^~~~~~[m[K
[7.534s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:249:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[7.534s]   249 | void* MapShow([01;35m[Kvoid *arg[m[K)
[7.534s]       |               [01;35m[K~~~~~~^~~[m[K
[7.564s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[7.565s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.565s]   304 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[7.565s]       |                 [01;35m[K^[m[K
[7.578s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.579s]   305 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[7.579s]       |                 [01;35m[K^[m[K
[7.593s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:306:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.593s]   306 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[7.593s]       |                 [01;35m[K^[m[K
[7.604s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.604s]   307 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[7.604s]       |                 [01;35m[K^[m[K
[7.604s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:308:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.604s]   308 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[7.604s]       |                 [01;35m[K^[m[K
[7.605s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:309:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.605s]   309 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[7.605s]       |                 [01;35m[K^[m[K
[7.605s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:310:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.605s]   310 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[7.605s]       |                 [01;35m[K^[m[K
[7.607s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:311:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.607s]   311 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[7.607s]       |                 [01;35m[K^[m[K
[7.607s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:312:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[7.610s]   312 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[7.612s]       |                 [01;35m[K^[m[K
[7.612s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:313:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[7.612s]   313 |             [01;35m[K}[m[K;
[7.612s]       |             [01;35m[K^[m[K
[7.612s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:326:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[7.612s]   326 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[7.612s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[7.786s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[7.787s]   406 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[7.787s]       |                                                            [01;35m[K^~~[m[K
[7.797s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[7.797s]   330 |     double [01;35m[Kstart[m[K, end;
[7.797s]       |            [01;35m[K^~~~~[m[K
[7.797s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:330:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.797s]   330 |     double start, [01;35m[Kend[m[K;
[7.797s]       |                   [01;35m[K^~~[m[K
[7.797s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.797s]   331 |     double [01;35m[Kduration[m[K[10];
[7.798s]       |            [01;35m[K^~~~~~~~[m[K
[7.798s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.798s]   332 |     int [01;35m[Kdatanum[m[K = 0;
[7.798s]       |         [01;35m[K^~~~~~~[m[K
[7.798s] [01m[K/home/<USER>/333/project2_0815/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[7.798s]   385 |     double [01;35m[Kanglevel[m[K[2];
[7.798s]       |            [01;35m[K^~~~~~~~[m[K
[24.348s] [100%] [32m[1mLinking CXX executable xt_user_node[0m
[26.204s] [100%] Built target xt_user_node
[26.229s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/333/project2_0815/USVControl-user/build/xt_user -- -j8 -l8
[26.246s] Invoking command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
[26.260s] -- Install configuration: ""
[26.261s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[26.261s] -- Set runtime path of "/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""
[26.261s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[26.280s] -- Set runtime path of "/home/<USER>/333/project2_0815/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[26.286s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[26.287s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.dsv
[26.287s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[26.287s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[26.287s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[26.287s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[26.287s] -- Installing: /home/<USER>/333/project2_0815/USVControl-user/install/xt_user/share/xt_user/package.xml
[26.290s] Invoked command in '/home/<USER>/333/project2_0815/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/333/project2_0815/USVControl-user/build/xt_user
