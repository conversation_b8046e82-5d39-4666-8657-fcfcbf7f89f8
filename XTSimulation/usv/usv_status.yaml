# version
Version: v1.2.0

# 船类别配置
ShipTypes:
  - type: 0                      # 第一类船
    count: 10                     # 数量
    isUsed: true
    package: xt_usv
    executable: xt_usv_node   
    output: screen
    node_frequency: 10
    rand_num_start: 1             # 随机数种子起始值
    # 位置和航向数组（按顺序对应每艘船）
    positions:
      - [800, 2000, 160]
      - [1500, 2100, 175]
      - [3000, 1500, 130]         # [posx, posy, pre_phi]
      - [4000, 1000, 145]
      - [5700, 500, 60]
      - [1000, 2000, 75]
      - [4000, 1000, 145]
      - [2100, 2000, 60]
      - [1000, 2000, 275]
      - [1500, 2100, 160]
  
  - type: 1                      # 第二类船
    count: 10
    isUsed: true
    package: xt_usv
    executable: xt_usv_node
    output: screen
    node_frequency: 10
    rand_num_start: 5
    positions:
      - [1700, 2000, 160]
      - [3100, 1500, 175]
      - [3400, 1100, 130]         # [posx, posy, pre_phi]
      - [5200, 900, 145]
      - [6500, 700, 60]
      - [2200, 1900, 75]
      - [4800, 1000, 145]
      - [3200, 1400, 60]
      - [2800, 1600, 275]
      - [3100, 1500, 160]
  
  - type: 2                      # 第三类船
    count: 1
    isUsed: true
    package: xt_usv
    executable: xt_usv_node
    output: screen
    node_frequency: 10
    rand_num_start: 8
    positions:
      - [12670, 1853, 0]
      # - [1600, 2600, 150]
      # - [1500, 2700, 165]