[0.106s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.106s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x79e5b5f387c0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x79e5b5f38250>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x79e5b5f38250>>)
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.156s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.156s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/haique_work/usv_control/project2/XTSimulation'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.156s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.157s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.160s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'ignore'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'ignore_ament_install'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extensions ['colcon_pkg']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'colcon_pkg'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extensions ['colcon_meta']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'colcon_meta'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extensions ['ros']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'ros'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extensions ['cmake', 'python']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'cmake'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'python'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extensions ['python_setup_py']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env) by extension 'python_setup_py'
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.161s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'ignore_ament_install'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extensions ['colcon_pkg']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'colcon_pkg'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extensions ['colcon_meta']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'colcon_meta'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extensions ['ros']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'ros'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extensions ['cmake', 'python']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'cmake'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'python'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extensions ['python_setup_py']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/__pycache__) by extension 'python_setup_py'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/install) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/install) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(env/install) ignored
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.162s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.163s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extensions ['ignore', 'ignore_ament_install']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'ignore'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'ignore_ament_install'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extensions ['colcon_pkg']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'colcon_pkg'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extensions ['colcon_meta']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'colcon_meta'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extensions ['ros']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'ros'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extensions ['cmake', 'python']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'cmake'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'python'
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extensions ['python_setup_py']
[0.164s] Level 1:colcon.colcon_core.package_identification:_identify(plan) by extension 'python_setup_py'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'ignore'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'ignore_ament_install'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extensions ['colcon_pkg']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'colcon_pkg'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extensions ['colcon_meta']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'colcon_meta'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extensions ['ros']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'ros'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extensions ['cmake', 'python']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'cmake'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'python'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extensions ['python_setup_py']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/__pycache__) by extension 'python_setup_py'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/install) by extensions ['ignore', 'ignore_ament_install']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/install) by extension 'ignore'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(plan/install) ignored
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extensions ['ignore', 'ignore_ament_install']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'ignore'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'ignore_ament_install'
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extensions ['colcon_pkg']
[0.165s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'ros'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extensions ['cmake', 'python']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'cmake'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'python'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extensions ['python_setup_py']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv) by extension 'python_setup_py'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'ignore'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'ignore_ament_install'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extensions ['colcon_pkg']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'colcon_pkg'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extensions ['colcon_meta']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'colcon_meta'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extensions ['ros']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'ros'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extensions ['cmake', 'python']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'cmake'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'python'
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extensions ['python_setup_py']
[0.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv/__pycache__) by extension 'python_setup_py'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(usv/install) by extensions ['ignore', 'ignore_ament_install']
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(usv/install) by extension 'ignore'
[0.167s] Level 1:colcon.colcon_core.package_identification:_identify(usv/install) ignored
[0.167s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.167s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.167s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.167s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.167s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.186s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.186s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.188s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 3 installed packages in /home/<USER>/haique_work/usv_control/project2/XTSimulation/install
[0.190s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 273 installed packages in /opt/ros/humble
[0.191s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.224s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.226s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.226s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.226s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.226s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.226s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.232s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.232s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.232s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.248s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.250s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.251s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/local_setup.ps1'
[0.252s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/_local_setup_util_ps1.py'
[0.253s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/setup.ps1'
[0.254s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/local_setup.sh'
[0.255s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/_local_setup_util_sh.py'
[0.256s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/setup.sh'
[0.257s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/local_setup.bash'
[0.257s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/setup.bash'
[0.258s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/local_setup.zsh'
[0.259s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/haique_work/usv_control/project2/XTSimulation/install/setup.zsh'
